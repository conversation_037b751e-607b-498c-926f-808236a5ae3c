# 美团项目构建完整指南

## 🚀 快速开始

### 一键修复构建问题
```bash
# 给脚本执行权限
chmod +x build-fix.sh check-env.sh build.sh

# 检查环境
./check-env.sh

# 修复所有问题并构建
./build-fix.sh

# 日常构建
./build.sh
```

## 📋 环境要求

### Node.js 版本
- **严格要求**: Node.js 16.14.0
- **验证命令**: `node --version` 应输出 `v16.14.0`
- **安装方法**: `nvm install 16.14.0 && nvm use 16.14.0`

### npm registry
- **要求**: 美团内部 registry
- **配置**: `npm config set registry http://r.npm.sankuai.com`
- **验证**: `npm config get registry` 应输出 `http://r.npm.sankuai.com`

### 网络要求
- 能够访问美团内部 npm registry
- 可能需要 VPN 或内网环境

## 🔧 手动构建步骤

### 1. 环境准备
```bash
# 设置 Node.js 版本（如果使用 nvm）
nvm use 16.14.0

# 验证版本
node --version  # 应输出 v16.14.0
npm --version   # 应输出对应的 npm 版本

# 设置 npm registry
npm config set registry http://r.npm.sankuai.com
```

### 2. 依赖安装
```bash
# 清理缓存（可选）
npm cache clean --force

# 安装依赖（使用 package-lock.json 确保版本一致）
npm ci  # 推荐，严格按照 package-lock.json 安装
# 或者
npm install
```

### 3. 构建
```bash
# 生产环境构建
npm run build

# 测试环境构建
npm run build:test
```

## 📦 关键依赖版本

### 美团内部依赖
- `@cs/multi-interaction-fe`: ^1.0.8
- `@mtfe/sso-web`: ^2.6.1
- `@cs/eslint-config`: ^2.0.32

### 构建工具
- `webpack`: 5.x
- `vue`: 3.x
- `sass`: ^1.62.0
- `eslint-import-resolver-typescript`: ^3.6.1 (兼容 Node.js 16)

## 🚨 已知问题及解决方案

### 1. ESLint 配置问题
**问题**: `prettier/vue` 配置已废弃
**解决**: 已在 `webpack/config.js` 中临时禁用 ESLint 插件

### 2. 依赖版本兼容性
**问题**: `eslint-import-resolver-typescript` 4.x 与 Node.js 16 不兼容
**解决**: 已降级到 3.6.1 版本

### 3. Node.js 版本冲突
**问题**: 系统使用了错误的 Node.js 版本
**解决**: 使用 NVM 管理版本，确保使用 16.14.0

## 🛠️ 故障排除

### 构建失败常见原因
1. **Node.js 版本不匹配**: 确保使用 16.14.0
2. **registry 无法访问**: 确保能访问 `http://r.npm.sankuai.com`
3. **依赖版本冲突**: 删除 `node_modules` 重新安装
4. **内存不足**: 增加 Node.js 内存限制 `--max-old-space-size=4096`
5. **ESLint 配置错误**: 已临时禁用，不影响构建

### 清理重建
```bash
# 完全清理
rm -rf node_modules package-lock.json
npm cache clean --force

# 重新安装
npm install

# 重新构建
npm run build
```

### 环境检查
```bash
# 检查所有环境配置
./check-env.sh

# 查看详细的构建环境信息
node --version
npm --version
npm config get registry
```

## 📁 输出目录
- **构建输出**: `./build/`
- **静态资源**: `./build/static/`
- **入口文件**: `./build/index.html`

## 🎯 服务器部署建议

### CI/CD 脚本示例
```bash
#!/bin/bash
# 服务器构建脚本

# 1. 设置环境
export NVM_DIR="$HOME/.nvm"
[ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh"
nvm use 16.14.0

# 2. 设置 registry
npm config set registry http://r.npm.sankuai.com

# 3. 安装依赖
npm ci

# 4. 构建
npm run build
```

## 📞 技术支持

如果遇到构建问题：
1. 首先运行 `./check-env.sh` 检查环境
2. 运行 `./build-fix.sh` 自动修复
3. 查看本文档的故障排除部分
4. 检查网络连接和 VPN 状态
