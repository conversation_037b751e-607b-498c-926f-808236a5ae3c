/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
import '@vue/runtime-core'

export {};

declare module '@vue/runtime-core' {
  export interface GlobalComponents {
    ElDropdown: (typeof import('element-plus/es'))['ElDropdown'];
    ElDropdownItem: (typeof import('element-plus/es'))['ElDropdownItem'];
    ElDropdownMenu: (typeof import('element-plus/es'))['ElDropdownMenu'];
    ElEmpty: (typeof import('element-plus/es'))['ElEmpty'];
    ElMenu: (typeof import('element-plus/es'))['ElMenu'];
    ElMenuItem: (typeof import('element-plus/es'))['ElMenuItem'];
    LayoutAside: (typeof import('./../src/components/Layout/LayoutAside/index.vue'))['default'];
    LayoutContent: (typeof import('./../src/components/Layout/LayoutContent/index.vue'))['default'];
    LayoutHeader: (typeof import('./../src/components/Layout/LayoutHeader/index.vue'))['default'];
    Modal: (typeof import('./../src/components/Modal.vue'))['default'];
    RouterLink: (typeof import('vue-router'))['RouterLink'];
    RouterView: (typeof import('vue-router'))['RouterView'];
  }
  export interface ComponentCustomProperties {
    vLoading: (typeof import('element-plus/es'))['ElLoadingDirective'];
  }
}
