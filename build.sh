#!/bin/bash

# 美团项目快速构建脚本
# 确保使用正确的 Node.js 版本和配置

set -e

echo "🚀 开始构建..."

# 1. 确保使用正确的 Node.js 版本
export NVM_DIR="$HOME/.nvm"
[ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh"

# 使用项目指定的 Node.js 版本
if [ -f ".nvmrc" ]; then
    echo "📦 切换到项目指定的 Node.js 版本..."
    nvm use
    export PATH="$HOME/.nvm/versions/node/v$(cat .nvmrc)/bin:$PATH"
else
    echo "📦 使用 Node.js 16.14.0..."
    nvm use 16.14.0 2>/dev/null || nvm install 16.14.0
    export PATH="$HOME/.nvm/versions/node/v16.14.0/bin:$PATH"
fi

# 2. 确保使用正确的 npm registry
echo "🔧 检查 npm registry..."
npm config set registry http://r.npm.sankuai.com

# 3. 显示当前环境信息
echo "📋 当前环境:"
echo "   Node.js: $(node --version)"
echo "   npm: $(npm --version)"
echo "   registry: $(npm config get registry)"

# 4. 构建
echo "🔨 开始构建..."
npm run build

echo "✅ 构建完成！"
