{"compilerOptions": {"useDefineForClassFields": true, "module": "ESNext", "moduleResolution": "Node", "strict": true, "jsx": "preserve", "resolveJsonModule": true, "isolatedModules": true, "esModuleInterop": true, "emitDecoratorMetadata": true, "experimentalDecorators": true, "skipLibCheck": true, "noEmit": true, "composite": true, "allowSyntheticDefaultImports": true, "strictPropertyInitialization": false, "baseUrl": ".", "types": ["node", "webpack-env", "chrome"], "target": "es5", "paths": {"@/*": ["src/*"]}, "lib": ["esnext", "dom", "dom.iterable"]}, "include": ["src/**/*.ts", "src/**/*.d.ts", "src/**/*.tsx", "src/**/*.vue", "f2eci.json", "types/**/*.d.ts", "postcss.config.ts"], "exclude": ["node_modules"]}