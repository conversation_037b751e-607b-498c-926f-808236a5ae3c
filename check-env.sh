#!/bin/bash

# 美团项目环境检查脚本
# 检查所有构建相关的环境配置

echo "🔍 检查构建环境..."

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m'

check_pass() {
    echo -e "  ✅ ${GREEN}$1${NC}"
}

check_fail() {
    echo -e "  ❌ ${RED}$1${NC}"
}

check_warn() {
    echo -e "  ⚠️  ${YELLOW}$1${NC}"
}

echo ""
echo "📋 环境检查报告"
echo "=================="

# 1. Node.js 版本检查
echo ""
echo "🟢 Node.js 版本:"
CURRENT_NODE=$(node --version)
REQUIRED_NODE="v16.14.0"

if [ "$CURRENT_NODE" = "$REQUIRED_NODE" ]; then
    check_pass "Node.js 版本正确: $CURRENT_NODE"
else
    check_fail "Node.js 版本不匹配"
    echo "     当前版本: $CURRENT_NODE"
    echo "     要求版本: $REQUIRED_NODE"
fi

# 2. npm 版本检查
echo ""
echo "🟢 npm 版本:"
NPM_VERSION=$(npm --version)
echo "  📦 npm 版本: $NPM_VERSION"

# 3. npm registry 检查
echo ""
echo "🟢 npm registry:"
CURRENT_REGISTRY=$(npm config get registry)
REQUIRED_REGISTRY="http://r.npm.sankuai.com"

if [ "$CURRENT_REGISTRY" = "$REQUIRED_REGISTRY" ]; then
    check_pass "registry 配置正确: $CURRENT_REGISTRY"
else
    check_fail "registry 配置错误"
    echo "     当前 registry: $CURRENT_REGISTRY"
    echo "     要求 registry: $REQUIRED_REGISTRY"
fi

# 4. registry 连通性检查
echo ""
echo "🟢 网络连通性:"
if curl -s --connect-timeout 5 $REQUIRED_REGISTRY > /dev/null; then
    check_pass "美团 registry 连接正常"
else
    check_warn "美团 registry 连接失败，可能需要 VPN"
fi

# 5. NVM 检查
echo ""
echo "🟢 NVM 状态:"
if command -v nvm &> /dev/null; then
    check_pass "NVM 已安装"
    
    # 检查是否有 16.14.0 版本
    if nvm list | grep -q "v16.14.0"; then
        check_pass "Node.js 16.14.0 已通过 NVM 安装"
    else
        check_warn "Node.js 16.14.0 未通过 NVM 安装"
    fi
else
    check_fail "NVM 未安装或未正确配置"
fi

# 6. 项目文件检查
echo ""
echo "🟢 项目文件:"
if [ -f "package.json" ]; then
    check_pass "package.json 存在"
else
    check_fail "package.json 不存在"
fi

if [ -f ".nvmrc" ]; then
    NVMRC_VERSION=$(cat .nvmrc)
    check_pass ".nvmrc 存在，指定版本: $NVMRC_VERSION"
else
    check_warn ".nvmrc 不存在"
fi

if [ -f "webpack/config.js" ]; then
    check_pass "webpack 配置存在"
else
    check_fail "webpack 配置不存在"
fi

# 7. 依赖检查
echo ""
echo "🟢 关键依赖:"
if [ -d "node_modules" ]; then
    check_pass "node_modules 存在"
    
    # 检查关键依赖
    if [ -d "node_modules/eslint-import-resolver-typescript" ]; then
        ESLINT_VERSION=$(npm list eslint-import-resolver-typescript --depth=0 2>/dev/null | grep eslint-import-resolver-typescript | sed 's/.*@//' || echo "unknown")
        if [[ "$ESLINT_VERSION" == "3."* ]]; then
            check_pass "eslint-import-resolver-typescript 版本兼容: $ESLINT_VERSION"
        else
            check_warn "eslint-import-resolver-typescript 版本可能不兼容: $ESLINT_VERSION"
        fi
    else
        check_warn "eslint-import-resolver-typescript 未安装"
    fi
else
    check_warn "node_modules 不存在，需要运行 npm install"
fi

echo ""
echo "=================="
echo "🎯 建议操作:"
echo ""

if [ "$CURRENT_NODE" != "$REQUIRED_NODE" ]; then
    echo "1. 修复 Node.js 版本:"
    echo "   nvm install 16.14.0 && nvm use 16.14.0"
    echo ""
fi

if [ "$CURRENT_REGISTRY" != "$REQUIRED_REGISTRY" ]; then
    echo "2. 修复 npm registry:"
    echo "   npm config set registry http://r.npm.sankuai.com"
    echo ""
fi

if [ ! -d "node_modules" ]; then
    echo "3. 安装依赖:"
    echo "   npm install"
    echo ""
fi

echo "4. 快速修复所有问题:"
echo "   ./build-fix.sh"
echo ""
echo "5. 日常构建:"
echo "   ./build.sh"
echo ""
