# 人际关系助手设计系统规范

## 目录

1. [设计理念](#设计理念)
2. [颜色系统](#颜色系统)
3. [排版系统](#排版系统)
4. [间距与尺寸](#间距与尺寸)
5. [组件规范](#组件规范)
6. [响应式设计](#响应式设计)
7. [使用指南](#使用指南)

## 设计理念

人际关系助手采用**赛博朋克/未来科技**风格，以深色背景和明亮的青色/蓝绿色为主要色调，营造科技感和未来感。设计系统注重：

- **一致性**：所有页面和组件保持视觉一致
- **可读性**：文字清晰可读，特别是在移动设备上
- **层次感**：明确的视觉层次，引导用户关注重点
- **科技感**：通过颜色、光效和动效营造未来科技感
- **移动优先**：所有设计元素优先考虑移动设备体验

## 颜色系统

### 主色调

| 变量名                   | 颜色值                   | 用途               |
| ------------------------ | ------------------------ | ------------------ |
| `--primary-color`        | `#00bcd4`                | 主要按钮、重点元素 |
| `--primary-color-light`  | `rgba(0, 188, 212, 0.1)` | 背景、悬停状态     |
| `--primary-color-medium` | `rgba(0, 188, 212, 0.2)` | 激活状态、边框     |
| `--primary-color-strong` | `rgba(0, 188, 212, 0.3)` | 阴影、强调         |

### 强调色

| 变量名                  | 颜色值                   | 用途               |
| ----------------------- | ------------------------ | ------------------ |
| `--accent-color`        | `#00ffff`                | 边框、高亮元素     |
| `--accent-color-light`  | `rgba(0, 255, 255, 0.1)` | 图标背景、轻微强调 |
| `--accent-color-medium` | `rgba(0, 255, 255, 0.2)` | 边框、分隔线       |
| `--accent-color-strong` | `rgba(0, 255, 255, 0.3)` | 阴影、强调         |

### 文字颜色

| 变量名             | 颜色值                     | 用途               |
| ------------------ | -------------------------- | ------------------ |
| `--text-primary`   | `#ffffff`                  | 主要文字、标题     |
| `--text-secondary` | `rgba(255, 255, 255, 0.9)` | 次要文字、副标题   |
| `--text-tertiary`  | `rgba(255, 255, 255, 0.8)` | 普通文字、描述     |
| `--text-disabled`  | `rgba(255, 255, 255, 0.6)` | 禁用状态、提示文字 |

### 背景色

| 变量名             | 颜色值                     | 用途             |
| ------------------ | -------------------------- | ---------------- |
| `--bg-glass`       | `rgba(30, 58, 138, 0.15)`  | 卡片背景、模态框 |
| `--bg-glass-hover` | `rgba(30, 58, 138, 0.25)`  | 悬停状态背景     |
| `--border-glass`   | `rgba(255, 255, 255, 0.2)` | 边框、分隔线     |
| `--border-accent`  | `rgba(0, 255, 255, 0.3)`   | 强调边框         |

### 阴影

| 变量名            | 颜色值                            | 用途               |
| ----------------- | --------------------------------- | ------------------ |
| `--shadow-soft`   | `0 8px 32px rgba(0, 0, 0, 0.2)`   | 轻微阴影、普通元素 |
| `--shadow-strong` | `0 20px 60px rgba(0, 0, 0, 0.3)`  | 强烈阴影、弹出层   |
| `--shadow-accent` | `0 0 20px rgba(0, 255, 255, 0.2)` | 强调阴影、重点元素 |

## 排版系统

### 字体家族

```css
font-family:
  -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
```

### 字体大小（桌面）

| 类名                   | 大小 | 行高 | 字重 | 用途           |
| ---------------------- | ---- | ---- | ---- | -------------- |
| `.cyber-title`         | 36px | 1.4  | 700  | 页面主标题     |
| `.cyber-subtitle`      | 20px | 1.4  | 500  | 页面副标题     |
| `.cyber-feature-title` | 22px | 1.3  | 600  | 功能卡片标题   |
| `.cyber-feature-desc`  | 18px | 1.5  | 400  | 功能卡片描述   |
| `.cyber-body`          | 16px | 1.5  | 400  | 正文内容       |
| `.cyber-caption`       | 14px | 1.4  | 400  | 辅助文字、说明 |

### 字体大小（移动端）

| 类名                   | 大小 | 行高 | 字重 | 用途           |
| ---------------------- | ---- | ---- | ---- | -------------- |
| `.cyber-title`         | 32px | 1.4  | 700  | 页面主标题     |
| `.cyber-subtitle`      | 18px | 1.4  | 500  | 页面副标题     |
| `.cyber-feature-title` | 20px | 1.3  | 600  | 功能卡片标题   |
| `.cyber-feature-desc`  | 16px | 1.5  | 400  | 功能卡片描述   |
| `.cyber-body`          | 16px | 1.5  | 400  | 正文内容       |
| `.cyber-caption`       | 14px | 1.4  | 400  | 辅助文字、说明 |

## 间距与尺寸

### 基础间距

| 类名          | 大小 | 用途         |
| ------------- | ---- | ------------ |
| `.gap-small`  | 8px  | 紧凑元素间距 |
| `.gap-medium` | 16px | 标准元素间距 |
| `.gap-large`  | 24px | 宽松元素间距 |
| `.mb-small`   | 8px  | 小型下边距   |
| `.mb-medium`  | 16px | 中型下边距   |
| `.mb-large`   | 24px | 大型下边距   |

### 容器内边距

| 容器类型 | 桌面内边距 | 移动内边距 |
| -------- | ---------- | ---------- |
| 卡片     | 20px       | 16px       |
| 模态框   | 30px       | 20px       |
| 功能卡片 | 20px       | 16px       |
| 按钮     | 18px 28px  | 16px 24px  |

### 边框圆角

| 元素         | 圆角大小   |
| ------------ | ---------- |
| 卡片、面板   | 16px       |
| 模态框、弹窗 | 20px       |
| 按钮         | 20px       |
| 输入框       | 12px       |
| 标签、徽章   | 16px       |
| 头像（小）   | 50% (圆形) |
| 头像（大）   | 50% (圆形) |

### 图标尺寸

| 用途     | 桌面尺寸 | 移动尺寸 |
| -------- | -------- | -------- |
| 导航图标 | 24px     | 24px     |
| 功能图标 | 36px     | 28px     |
| 按钮图标 | 20px     | 18px     |
| 状态图标 | 16px     | 16px     |

## 组件规范

### 按钮

#### 主要按钮 (`.cyber-btn-primary`)

```css
.cyber-btn-primary {
  padding: 18px 28px;
  border-radius: 20px;
  font-size: 18px;
  font-weight: 600;
  color: var(--primary-color);
  border: 2px solid var(--primary-color);
  background: var(--primary-color-light);
}
```

#### 次要按钮 (`.cyber-btn-secondary`)

```css
.cyber-btn-secondary {
  padding: 18px 28px;
  border-radius: 20px;
  font-size: 18px;
  font-weight: 600;
  color: var(--text-secondary);
  border: 2px solid var(--border-glass);
  background: transparent;
}
```

### 卡片

#### 基础卡片 (`.cyber-card`)

```css
.cyber-card {
  background: var(--bg-glass);
  border: 2px solid var(--border-accent);
  border-radius: 16px;
  padding: 20px;
  backdrop-filter: blur(20px);
  box-shadow: var(--shadow-strong), var(--shadow-accent);
}
```

#### 功能卡片 (`.cyber-feature-card`)

```css
.cyber-feature-card {
  display: flex;
  align-items: center;
  gap: 20px;
  padding: 20px;
  background: var(--primary-color-light);
  border-radius: 16px;
  border-left: 4px solid var(--accent-color-medium);
}
```

### 弹窗/模态框

#### 基础弹窗 (`.cyber-modal`)

```css
.cyber-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--overlay-dark);
  backdrop-filter: blur(20px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.cyber-modal-content {
  background: var(--bg-glass);
  border: 2px solid var(--border-accent);
  border-radius: 20px;
  padding: 30px;
  backdrop-filter: blur(20px);
  box-shadow: var(--shadow-strong), var(--shadow-accent);
  max-width: 90vw;
  max-height: 80vh;
  overflow: hidden;
}
```

### 图标容器

```css
.cyber-feature-icon {
  font-size: 36px;
  width: 64px;
  height: 64px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--accent-color-light);
  border-radius: 50%;
}
```

## 响应式设计

### 断点

| 断点名称 | 宽度范围       |
| -------- | -------------- |
| 移动端   | < 768px        |
| 平板     | 768px - 1024px |
| 桌面     | > 1024px       |

### 响应式调整

```css
@media (max-width: 768px) {
  .cyber-title {
    font-size: 32px;
  }

  .cyber-subtitle {
    font-size: 18px;
  }

  .cyber-feature-title {
    font-size: 20px;
  }

  .cyber-feature-desc {
    font-size: 16px;
  }

  .cyber-card {
    padding: 16px;
    border-radius: 12px;
  }

  .cyber-feature-card {
    padding: 16px;
    gap: 16px;
  }

  .cyber-feature-icon {
    font-size: 28px;
    width: 48px;
    height: 48px;
  }

  .cyber-btn {
    padding: 16px 24px;
    font-size: 16px;
  }
}
```

## 使用指南

### 引入设计系统

在项目中引入设计系统：

```scss
// 在主样式文件中引入
@use '@/styles/design-system.scss';
```

### 使用示例

```vue
<template>
  <div class="cyber-card">
    <h1 class="cyber-title">欢迎使用</h1>
    <p class="cyber-subtitle">人际关系助手</p>

    <div class="cyber-feature-card">
      <div class="cyber-feature-icon">💬</div>
      <div>
        <h2 class="cyber-feature-title">智能对话</h2>
        <p class="cyber-feature-desc">与我聊天，分享你身边的人和事</p>
      </div>
    </div>

    <div class="gap-medium"></div>

    <button class="cyber-btn-primary">开始使用</button>
  </div>
</template>
```

### 最佳实践

1. **使用预定义类**：优先使用设计系统中的类，而不是自定义样式
2. **保持一致性**：遵循设计系统的颜色、字体和间距规范
3. **移动优先**：先设计移动端界面，再扩展到桌面端
4. **避免硬编码**：使用CSS变量而不是硬编码的颜色值和尺寸
5. **组件化思维**：将UI元素视为可复用的组件，保持一致的样式

## 详细组件规范

### 输入框

#### 基础输入框

```css
.cyber-input {
  padding: 12px 16px;
  border-radius: 12px;
  border: 2px solid var(--border-glass);
  background: var(--bg-glass);
  color: var(--text-primary);
  font-size: 16px;
  transition: all 0.3s ease;
}

.cyber-input:focus {
  border-color: var(--accent-color);
  box-shadow: 0 0 0 3px var(--accent-color-light);
}
```

#### 输入框尺寸

| 尺寸 | 高度 | 内边距    | 字体大小 |
| ---- | ---- | --------- | -------- |
| 小型 | 36px | 8px 12px  | 14px     |
| 标准 | 44px | 12px 16px | 16px     |
| 大型 | 52px | 16px 20px | 18px     |

### 头像

#### 头像尺寸

| 用途           | 桌面尺寸 | 移动尺寸 |
| -------------- | -------- | -------- |
| 用户头像（小） | 32px     | 28px     |
| 用户头像（中） | 48px     | 40px     |
| 用户头像（大） | 80px     | 64px     |
| 助手头像       | 120px    | 80px     |

#### 头像样式

```css
.cyber-avatar {
  border-radius: 50%;
  object-fit: cover;
  border: 3px solid var(--accent-color-medium);
  box-shadow: var(--shadow-accent);
}
```

### 标签和徽章

#### 状态标签

```css
.cyber-badge {
  padding: 4px 12px;
  border-radius: 16px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.cyber-badge-primary {
  background: var(--primary-color-light);
  color: var(--primary-color);
  border: 1px solid var(--primary-color);
}

.cyber-badge-success {
  background: rgba(76, 175, 80, 0.1);
  color: #4caf50;
  border: 1px solid #4caf50;
}

.cyber-badge-warning {
  background: rgba(255, 193, 7, 0.1);
  color: #ffc107;
  border: 1px solid #ffc107;
}
```

### 加载状态

#### 加载动画

```css
.cyber-loading {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 2px solid var(--border-glass);
  border-radius: 50%;
  border-top-color: var(--accent-color);
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}
```

### 分隔线

```css
.cyber-divider {
  height: 1px;
  background: var(--border-glass);
  margin: 16px 0;
}

.cyber-divider-accent {
  height: 2px;
  background: linear-gradient(90deg, transparent, var(--accent-color), transparent);
  margin: 24px 0;
}
```

## 动效规范

### 过渡动画

| 属性        | 时长  | 缓动函数 | 用途       |
| ----------- | ----- | -------- | ---------- |
| `all`       | 0.3s  | ease     | 通用过渡   |
| `transform` | 0.2s  | ease-out | 位移、缩放 |
| `opacity`   | 0.15s | ease     | 透明度变化 |
| `color`     | 0.2s  | ease     | 颜色变化   |

### 悬停效果

```css
.cyber-hover-lift {
  transition: transform 0.2s ease-out;
}

.cyber-hover-lift:hover {
  transform: translateY(-2px);
}

.cyber-hover-glow {
  transition: box-shadow 0.3s ease;
}

.cyber-hover-glow:hover {
  box-shadow: 0 0 20px var(--accent-color-medium);
}
```

### 点击反馈

```css
.cyber-clickable {
  transition: transform 0.1s ease;
}

.cyber-clickable:active {
  transform: scale(0.98);
}
```

## 布局规范

### 网格系统

#### 容器宽度

| 断点   | 最大宽度 | 内边距 |
| ------ | -------- | ------ |
| 移动端 | 100%     | 16px   |
| 平板   | 768px    | 24px   |
| 桌面   | 1200px   | 32px   |

#### 栅格间距

| 断点   | 列间距 |
| ------ | ------ |
| 移动端 | 16px   |
| 平板   | 20px   |
| 桌面   | 24px   |

### 页面布局

#### 标准页面结构

```vue
<template>
  <div class="cyber-page">
    <header class="cyber-header">
      <!-- 页面头部 -->
    </header>

    <main class="cyber-main">
      <div class="cyber-container">
        <!-- 主要内容 -->
      </div>
    </main>

    <footer class="cyber-footer">
      <!-- 页面底部 -->
    </footer>
  </div>
</template>
```

#### 卡片布局

```vue
<template>
  <div class="cyber-card-grid">
    <div class="cyber-card">
      <!-- 卡片内容 -->
    </div>
  </div>
</template>

<style>
.cyber-card-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 24px;
}

@media (max-width: 768px) {
  .cyber-card-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }
}
</style>
```

## 可访问性规范

### 颜色对比度

所有文字与背景的对比度必须满足：

- 普通文字：至少 4.5:1
- 大文字（18px+）：至少 3:1
- 图标和重要元素：至少 3:1

### 焦点状态

```css
.cyber-focusable:focus {
  outline: 2px solid var(--accent-color);
  outline-offset: 2px;
}
```

### 键盘导航

确保所有交互元素都可以通过键盘访问：

- Tab 键导航
- Enter/Space 键激活
- Escape 键关闭模态框

## 错误处理

### 错误状态样式

```css
.cyber-error {
  color: #f44336;
  border-color: #f44336;
  background: rgba(244, 67, 54, 0.1);
}

.cyber-error-message {
  color: #f44336;
  font-size: 14px;
  margin-top: 4px;
}
```

### 空状态

```css
.cyber-empty-state {
  text-align: center;
  padding: 48px 24px;
  color: var(--text-tertiary);
}

.cyber-empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.6;
}
```

## 更新日志

### v1.0.0 (2024-01-15)

- 初始版本发布
- 建立基础颜色系统
- 定义排版规范
- 创建组件库

### 维护指南

1. **定期审查**：每月审查设计系统的使用情况
2. **版本控制**：重大更改需要版本号更新
3. **文档同步**：代码更改后及时更新文档
4. **团队培训**：确保团队成员了解最新规范
