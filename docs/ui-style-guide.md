# UI 风格指南 - 赛博朋克/未来科技风格

## 概述

本项目采用**赛博朋克/未来科技**风格的UI设计，通过半透明材质、霓虹发光效果和毛玻璃质感，创造出具有科技感和未来感的用户界面。

## 核心设计原则

### 1. 材质系统

- **半透明背景** - 使用低透明度创造层次感
- **毛玻璃效果** - 通过backdrop-filter实现现代质感
- **发光边框** - 霓虹效果增强科技感

### 2. 颜色体系

- **主色调**: 青色/蓝绿色系
- **透明度层次**: 5% → 10% → 30% → 50%
- **发光强调**: 高饱和度青色用于重点元素

### 3. 交互反馈

- **悬停效果** - 浮起动画 + 发光增强
- **平滑过渡** - 0.3秒缓动动画
- **视觉层次** - 通过阴影和透明度区分

## 颜色规范

### 主色调

```scss
// 深青色 - 主背景色
$primary-cyan: rgba(0, 188, 212, 0.05);
$primary-cyan-hover: rgba(0, 188, 212, 0.1);

// 亮青色 - 发光/强调色
$neon-cyan: #00ffff;
$neon-glow: rgba(0, 255, 255, 0.3);
$neon-glow-hover: rgba(0, 255, 255, 0.5);
```

### 透明度层级

```scss
$opacity-subtle: 0.05; // 背景
$opacity-light: 0.1; // 悬停背景
$opacity-medium: 0.3; // 发光效果
$opacity-strong: 0.5; // 强调发光
```

## 组件样式模板

### 基础卡片组件

```scss
.cyber-card {
  // 基础结构
  background: rgba(0, 188, 212, 0.05);
  border: none;
  border-radius: 16px;
  padding: 20px;
  box-sizing: border-box;

  // 毛玻璃效果
  backdrop-filter: blur(10px);

  // 霓虹边框
  border-left: 4px solid #00ffff;
  box-shadow: -4px 0 8px rgba(0, 255, 255, 0.3);

  // 交互
  transition: all 0.3s ease;
  cursor: pointer;

  &:hover {
    background: rgba(0, 188, 212, 0.1);
    transform: translateY(-2px);
    box-shadow:
      -4px 0 8px rgba(0, 255, 255, 0.5),
      0 8px 25px rgba(0, 0, 0, 0.3);
  }
}
```

### 文字样式

```scss
// 主标题 - 白色高对比 (移动端优化)
.cyber-title {
  color: #ffffff;
  font-size: 24px; // 增大以适应移动端
  font-weight: 700;
  line-height: 1.4;
}

// 次要文字 - 半透明白色 (移动端优化)
.cyber-subtitle {
  color: rgba(255, 255, 255, 0.9); // 提高对比度
  font-size: 20px; // 增大以适应移动端
  font-weight: 500;
}

// 功能标题 - 中等大小
.cyber-feature-title {
  color: #ffffff;
  font-size: 22px; // 新增功能标题规范
  font-weight: 600;
}

// 功能描述 - 适中大小
.cyber-feature-desc {
  color: rgba(255, 255, 255, 0.8);
  font-size: 18px; // 新增功能描述规范
  font-weight: 400;
  line-height: 1.5;
}

// 辅助文字 - 低透明度 (移动端优化)
.cyber-caption {
  color: rgba(255, 255, 255, 0.7);
  font-size: 16px; // 增大以适应移动端
  font-weight: 400;
}
```

### 状态标签

```scss
.cyber-badge {
  padding: 4px 8px;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;

  // 积极状态
  &.positive {
    background: rgba(34, 197, 94, 0.2);
    color: #22c55e;
    border: 1px solid rgba(34, 197, 94, 0.3);
  }

  // 消极状态
  &.negative {
    background: rgba(239, 68, 68, 0.2);
    color: #ef4444;
    border: 1px solid rgba(239, 68, 68, 0.3);
  }

  // 中性状态
  &.neutral {
    background: rgba(156, 163, 175, 0.2);
    color: #9ca3af;
    border: 1px solid rgba(156, 163, 175, 0.3);
  }
}
```

## 动画效果

### 悬停动画

```scss
@mixin hover-lift {
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
  }
}
```

### 发光动画

```scss
@mixin neon-glow($color: #00ffff) {
  box-shadow: 0 0 8px rgba($color, 0.3);

  &:hover {
    box-shadow: 0 0 12px rgba($color, 0.5);
  }
}
```

## 使用示例

### EventItem 组件

这是风格的典型应用，展示了：

- 半透明青色背景
- 左侧霓虹边框
- 毛玻璃效果
- 悬停时的浮起和发光增强

### 适用场景

- **科技产品界面** - 仪表板、监控面板
- **游戏UI** - 未来科幻主题
- **创意网站** - 展示科技感和现代感
- **数据可视化** - 突出重要信息

## 最佳实践

### DO ✅

- 保持透明度层次的一致性
- 使用统一的圆角半径 (12px-16px)
- 确保文字对比度足够
- 适度使用发光效果，避免过度

### DON'T ❌

- 不要过度使用高透明度
- 避免混合过多颜色
- 不要忽略可访问性
- 避免过于复杂的动画

## 响应式适配

```scss
@media (max-width: 768px) {
  .cyber-card {
    padding: 16px;
    border-radius: 12px;
  }

  .cyber-title {
    font-size: 20px;
  }

  .cyber-subtitle {
    font-size: 16px;
  }
}
```

## 浏览器兼容性

- **backdrop-filter**: 需要现代浏览器支持
- **box-shadow**: 广泛支持
- **rgba**: 广泛支持
- **transform**: 广泛支持

建议为不支持backdrop-filter的浏览器提供降级方案。
