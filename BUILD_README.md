# 🚀 美团项目构建工具

这个项目包含了一套完整的构建工具，可以自动解决所有常见的构建问题。

## 📁 文件说明

| 文件 | 用途 | 使用场景 |
|------|------|----------|
| `check-env.sh` | 环境检查 | 诊断构建环境问题 |
| `build-fix.sh` | 一键修复 | 首次构建或遇到问题时 |
| `build.sh` | 快速构建 | 日常开发构建 |
| `BUILD_GUIDE.md` | 详细文档 | 了解技术细节和手动操作 |

## 🎯 使用方法

### 1. 首次使用或遇到问题
```bash
# 给脚本执行权限（只需要执行一次）
chmod +x *.sh

# 检查环境问题
./check-env.sh

# 一键修复所有问题并构建
./build-fix.sh
```

### 2. 日常构建
```bash
# 快速构建（推荐）
./build.sh
```

### 3. 手动构建
```bash
# 确保使用正确的 Node.js 版本
nvm use 16.14.0

# 设置正确的 registry
npm config set registry http://r.npm.sankuai.com

# 构建
npm run build
```

## 🔧 脚本功能详解

### `check-env.sh` - 环境检查
- ✅ 检查 Node.js 版本是否为 16.14.0
- ✅ 检查 npm registry 配置
- ✅ 测试网络连通性
- ✅ 检查 NVM 安装状态
- ✅ 检查项目文件完整性
- ✅ 检查关键依赖版本

### `build-fix.sh` - 一键修复
- 🔧 自动切换到正确的 Node.js 版本
- 🔧 修复 npm registry 配置
- 🔧 降级不兼容的依赖版本
- 🔧 修复 ESLint 配置问题
- 🔧 清理并重新安装依赖
- 🔧 执行构建

### `build.sh` - 快速构建
- 🚀 确保使用正确的 Node.js 版本
- 🚀 设置正确的 npm registry
- 🚀 显示环境信息
- 🚀 执行构建

## 🚨 常见问题解决

### 问题1: Node.js 版本错误
```bash
# 解决方法
nvm install 16.14.0
nvm use 16.14.0
```

### 问题2: npm registry 无法访问
```bash
# 检查网络连接
curl -I http://r.npm.sankuai.com

# 可能需要连接 VPN 或确保在内网环境
```

### 问题3: 依赖安装失败
```bash
# 清理并重新安装
rm -rf node_modules package-lock.json
npm cache clean --force
npm install
```

### 问题4: ESLint 错误
```bash
# 已自动修复，ESLint 插件已临时禁用
# 不影响构建，只影响代码检查
```

## 📊 构建成功标志

构建成功后，你会看到：
- ✅ 绿色的成功消息
- 📁 `build/` 目录被创建
- 📄 `build/index.html` 文件存在
- 📦 静态资源在 `build/static/` 目录

## 🎯 服务器部署

在服务器上使用时：
```bash
# 1. 上传项目文件
# 2. 给脚本执行权限
chmod +x *.sh

# 3. 一键构建
./build-fix.sh
```

## 💡 提示

1. **首次使用**: 运行 `./build-fix.sh`
2. **日常开发**: 运行 `./build.sh`
3. **遇到问题**: 先运行 `./check-env.sh` 诊断
4. **服务器部署**: 使用 `./build-fix.sh` 确保环境正确

## 📞 技术支持

如果脚本无法解决问题：
1. 查看 `BUILD_GUIDE.md` 了解详细信息
2. 检查网络连接和 VPN 状态
3. 确认是否在美团内网环境
4. 手动执行构建步骤进行调试
