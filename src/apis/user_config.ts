import http from '@/lib/http';

// 保存颜色主题配置
export interface ISaveColorThemeRequest {
  user_id: string;
  color_theme: string; // 主题ID，例如 'cyberpunk'
}

export interface ISaveColorThemeResponse {
  result: 'success' | 'fail';
  message?: string;
  config?: {
    user_id: string;
    color_theme: string;
  };
}

export function saveColorTheme(params: ISaveColorThemeRequest): Promise<ISaveColorThemeResponse> {
  return http({
    url: '/humanrelation/user_config/color_theme/save',
    method: 'post',
    data: params,
  });
}

// 获取颜色主题配置
export interface IGetColorThemeResponse {
  result: 'success' | 'fail';
  config?: {
    user_id: string;
    color_theme: string; // 主题ID
    color_theme_info?: {
      name?: string;
      description?: string;
    };
    last_updated?: string;
  };
}

export function getColorTheme(userId: string): Promise<IGetColorThemeResponse> {
  return http({
    url: `/humanrelation/user_config/color_theme/${userId}`,
    method: 'get',
  });
}
