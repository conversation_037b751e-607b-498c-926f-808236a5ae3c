import fetchInstance from '@/lib/fetch';

// 工具函数：将嵌套的key_attributes对象扁平化为键值对
export const flattenKeyAttributes = (attributes: unknown): Record<string, string> => {
  const result: Record<string, string> = {};

  const flatten = (obj: unknown) => {
    if (obj === null || obj === undefined) {
      return;
    }

    if (typeof obj === 'string') {
      // 如果是字符串，尝试解析为JSON
      try {
        const parsed = JSON.parse(obj);
        flatten(parsed);
      } catch {
        // 如果解析失败，忽略这个值
        return;
      }
      return;
    }

    if (typeof obj !== 'object' || Array.isArray(obj)) {
      // 如果是数组或非对象类型，忽略
      return;
    }

    // 遍历对象的所有键值对
    Object.entries(obj as Record<string, unknown>).forEach(([key, value]) => {
      if (value === null || value === undefined || value === '') {
        // 跳过空值
        return;
      }

      if (typeof value === 'object' && !Array.isArray(value)) {
        // 如果值是对象，递归处理
        flatten(value);
      } else {
        // 如果值是基本类型或数组，直接添加到结果中
        result[key] = Array.isArray(value) ? value.join(', ') : String(value);
      }
    });
  };

  flatten(attributes);
  return result;
};

// 后端返回的人员数据接口
export interface IPersonData {
  person_id: string;
  user_id: string;
  canonical_name: string;
  aliases: string;
  relationships: string[] | string; // 支持字符串和数组两种格式
  profile_summary: string;
  key_attributes: Record<string, string> | string; // 支持字符串和扁平化对象格式
  avatar: string;
  is_user: boolean;
}

// 后端API响应接口
export interface IPersonsResponse {
  result: string;
  persons: IPersonData[];
}

// 3D关系图节点接口
export interface IGraphNode {
  id: string;
  group: number;
  color?: string;
  [key: string]: unknown;
}

// 3D关系图连接接口
export interface IGraphLink {
  source: string;
  target: string;
  value: number;
  [key: string]: unknown;
}

// 3D关系图数据接口
export interface IGraphData {
  nodes: IGraphNode[];
  links: IGraphLink[];
}

// 获取人员关系数据的请求参数
export interface IGetPersonsParams {
  userId: string;
  limit?: number;
  offset?: number;
}

// 获取人员关系数据
export const getPersons = async (params: IGetPersonsParams): Promise<IPersonsResponse> => {
  const { userId, limit = 100, offset = 0 } = params;

  try {
    const response = await fetchInstance.fetch('/humanrelation/persons', {
      method: 'GET',
      params: {
        user_id: userId, // 后端API需要user_id格式
        limit,
        offset,
      },
    });

    // 处理每个人员的key_attributes和relationships字段
    if (response && response.persons && Array.isArray(response.persons)) {
      response.persons.forEach((person: IPersonData) => {
        // 处理key_attributes字段 - 使用扁平化函数
        if (person.key_attributes) {
          try {
            person.key_attributes = flattenKeyAttributes(person.key_attributes);
            console.log(
              '✅ [relation.ts] key_attributes扁平化成功:',
              person.canonical_name,
              person.key_attributes,
            );
          } catch (parseError) {
            console.warn(
              '⚠️ [relation.ts] key_attributes扁平化失败:',
              person.canonical_name,
              parseError,
            );
            person.key_attributes = {};
          }
        }

        // 处理relationships字段
        if (person.relationships && typeof person.relationships === 'string') {
          try {
            person.relationships = JSON.parse(person.relationships);
            console.log(
              '✅ [relation.ts] relationships JSON解析成功:',
              person.canonical_name,
              person.relationships,
            );
          } catch (parseError) {
            console.warn(
              '⚠️ [relation.ts] relationships JSON解析失败:',
              person.canonical_name,
              parseError,
            );
            person.relationships = [];
          }
        }
      });

      // 根据更新时间进行降序排序（若无更新时间则保持当前相对顺序）
      try {
        // 提取更新时间的通用方法：支持多种常见字段名以及中文“更新时间”，并兼容时间戳/ISO字符串
        const getUpdateTs = (p: IPersonData): number => {
          const anyP = p as unknown as Record<string, unknown>;
          const keyAttrs = (p.key_attributes || {}) as Record<string, string>;
          const rawTime =
            (anyP.updated_at as string | number | undefined) ||
            (anyP.update_time as string | number | undefined) ||
            (anyP.modified_at as string | number | undefined) ||
            (anyP.last_updated as string | number | undefined) ||
            keyAttrs['更新时间'] ||
            keyAttrs.updated_at ||
            keyAttrs.last_updated;

          if (rawTime === undefined || rawTime === null || rawTime === '') return 0;
          if (typeof rawTime === 'number') return rawTime;
          const parsed = Date.parse(String(rawTime));
          return Number.isNaN(parsed) ? 0 : parsed;
        };

        // 稳定排序：先映射索引确保无时间时保持原相对顺序
        response.persons = response.persons
          .map((p: IPersonData, idx: number) => ({
            p,
            idx,
            ts: getUpdateTs(p),
          }))
          .sort(
            (
              a: { p: IPersonData; idx: number; ts: number },
              b: { p: IPersonData; idx: number; ts: number },
            ) => {
              if (b.ts !== a.ts) return b.ts - a.ts; // 时间新的在前
              return a.idx - b.idx; // 保持稳定
            },
          )
          .map((x: { p: IPersonData }) => x.p);
      } catch (sortErr) {
        console.warn('⚠️ [relation.ts] 人员按更新时间排序失败，忽略排序:', sortErr);
      }
    }

    return response;
  } catch (error) {
    console.error('❌ [relation.ts] 获取人员关系数据失败:', error);
    throw error;
  }
};

// 将后端数据转换为3D关系图数据格式
export const transformPersonsToGraphData = (
  personsData: IPersonData[],
  currentUserId: string,
): IGraphData => {
  const nodes: IGraphNode[] = [];
  const links: IGraphLink[] = [];

  // 创建核心用户节点（自己）
  const coreNode: IGraphNode = {
    id: currentUserId,
    group: 0, // 核心用户为group 0
    color: '#ff6b6b', // 核心用户使用特殊颜色
  };
  nodes.push(coreNode);

  // 处理其他人员数据
  personsData.forEach((person) => {
    // 跳过自己
    if (person.is_user || person.canonical_name === currentUserId) {
      return;
    }

    // 创建节点 - 所有人员都使用相同的group
    const node: IGraphNode = {
      id: person.canonical_name,
      group: 1, // 所有其他人员都为group 1
    };
    nodes.push(node);

    // 创建从核心用户到该节点的连接
    // 使用随机连接强度来改善3D图形的力学稳定性，避免节点抖动
    const randomStrength = Math.random() * 2 + 0.5; // 0.5-2.5之间的随机值
    const link: IGraphLink = {
      source: currentUserId,
      target: person.canonical_name,
      value: randomStrength, // 随机连接强度，改善力学模拟稳定性
    };
    links.push(link);
  });

  return {
    nodes,
    links,
  };
};

// 添加人员的请求参数接口
export interface IAddPersonParams {
  user_id: string;
  canonical_name: string;
  aliases?: string;
  relationships?: string[];
  profile_summary?: string;
  key_attributes?: Record<string, unknown>;
  avatar?: string;
  is_user?: boolean;
}

// 添加人员的响应接口
export interface IAddPersonResponse {
  result: string;
  person_id: string;
}

// 删除人员的响应接口
export interface IDeletePersonResponse {
  result: string;
}

// 更新人员的请求参数接口
export interface IUpdatePersonParams {
  user_id: string;
  canonical_name: string;
  aliases?: string;
  relationships?: string[];
  profile_summary?: string;
  key_attributes?: Record<string, unknown>;
  avatar?: string;
  is_user?: boolean;
}

// 更新人员的响应接口
export interface IUpdatePersonResponse {
  result: string;
}

// 搜索人员的请求参数接口
export interface ISearchPersonParams {
  user_id: string;
  name: string;
  limit?: number;
}

// 搜索人员的响应接口
export interface ISearchPersonResponse {
  result: string;
  persons: IPersonData[];
}

// 获取用户档案的请求参数接口
export interface IGetUserProfileParams {
  user_id: string;
}

// 获取用户档案的响应接口
export interface IGetUserProfileResponse {
  result: string;
  person?: IPersonData;
  reason?: string;
}

// 添加人员
export const addPerson = (params: IAddPersonParams): Promise<IAddPersonResponse> => {
  return fetchInstance.fetch('/humanrelation/add_person', {
    method: 'POST',
    body: JSON.stringify({
      user_id: params.user_id,
      canonical_name: params.canonical_name,
      aliases: params.aliases || '',
      relationships: params.relationships || [],
      profile_summary: params.profile_summary || '',
      key_attributes: params.key_attributes || {},
      avatar: params.avatar || '',
      is_user: params.is_user || false,
    }),
  });
};

// 删除人员
export const deletePerson = (userId: string, personId: string): Promise<IDeletePersonResponse> => {
  return fetchInstance.fetch(`/humanrelation/person/${personId}`, {
    method: 'DELETE',
    params: {
      user_id: userId,
    },
  });
};

// 更新人员
export const updatePerson = (
  personId: string,
  params: IUpdatePersonParams,
): Promise<IUpdatePersonResponse> => {
  return fetchInstance.fetch(`/humanrelation/change_person/${personId}`, {
    method: 'PUT',
    body: JSON.stringify({
      user_id: params.user_id,
      canonical_name: params.canonical_name,
      aliases: params.aliases || '',
      relationships: params.relationships || [],
      profile_summary: params.profile_summary || '',
      key_attributes: params.key_attributes || {},
      avatar: params.avatar || '',
      is_user: params.is_user || false,
    }),
  });
};

// 搜索人员
export const searchPerson = async (params: ISearchPersonParams): Promise<ISearchPersonResponse> => {
  const { user_id: userId, name, limit = 10 } = params;

  try {
    const response = await fetchInstance.fetch('/humanrelation/search_person', {
      method: 'GET',
      params: {
        user_id: userId, // 后端API需要user_id格式
        name,
        limit,
      },
    });

    // 处理每个人员的key_attributes和relationships字段
    if (response && response.persons && Array.isArray(response.persons)) {
      response.persons.forEach((person: IPersonData) => {
        // 处理key_attributes字段 - 使用扁平化函数
        if (person.key_attributes) {
          try {
            person.key_attributes = flattenKeyAttributes(person.key_attributes);
            console.log(
              '✅ [relation.ts] searchPerson key_attributes扁平化成功:',
              person.canonical_name,
              person.key_attributes,
            );
          } catch (parseError) {
            console.warn(
              '⚠️ [relation.ts] searchPerson key_attributes扁平化失败:',
              person.canonical_name,
              parseError,
            );
            person.key_attributes = {};
          }
        }

        // 处理relationships字段
        if (person.relationships && typeof person.relationships === 'string') {
          try {
            person.relationships = JSON.parse(person.relationships);
            console.log(
              '✅ [relation.ts] searchPerson relationships JSON解析成功:',
              person.canonical_name,
              person.relationships,
            );
          } catch (parseError) {
            console.warn(
              '⚠️ [relation.ts] searchPerson relationships JSON解析失败:',
              person.canonical_name,
              parseError,
            );
            person.relationships = [];
          }
        }
      });
    }

    return response;
  } catch (error) {
    console.error('❌ [relation.ts] 搜索人员失败:', error);
    throw error;
  }
};

// 获取并转换关系图数据的便捷方法
export const getRelationGraphData = async (
  currentUserId: string,
  limit = 100,
  offset = 0,
): Promise<IGraphData> => {
  try {
    console.log('开始获取人员关系数据:', { currentUserId, limit, offset });
    const response = await getPersons({
      userId: currentUserId,
      limit,
      offset,
    });
    console.log('获取人员关系数据响应:', response);
    if (response.result === 'success' && response.persons) {
      console.log('获取人员关系数据成功:', response.persons);
      return transformPersonsToGraphData(response.persons, currentUserId);
    }
    console.warn('获取人员关系数据失败:', response);
    // 返回空数据
    return {
      nodes: [
        {
          id: currentUserId,
          group: 0,
          color: '#ff6b6b',
        },
      ],
      links: [],
    };
  } catch (error) {
    console.error('获取关系图数据失败:', error);
    // 返回空数据
    return {
      nodes: [
        {
          id: currentUserId,
          group: 0,
          color: '#ff6b6b',
        },
      ],
      links: [],
    };
  }
};

// 获取用户档案
export const getUserProfile = async (
  params: IGetUserProfileParams,
): Promise<IGetUserProfileResponse> => {
  const { user_id: userId } = params;

  try {
    const response = await fetchInstance.fetch('/humanrelation/get_user_profile', {
      method: 'GET',
      params: {
        user_id: userId, // 后端API需要user_id格式
      },
    });

    // 处理用户档案的key_attributes和relationships字段
    if (response && response.person) {
      const { person } = response;

      // 处理key_attributes字段 - 使用扁平化函数
      if (person.key_attributes) {
        try {
          person.key_attributes = flattenKeyAttributes(person.key_attributes);
          console.log(
            '✅ [relation.ts] getUserProfile key_attributes扁平化成功:',
            person.canonical_name,
            person.key_attributes,
          );
        } catch (parseError) {
          console.warn(
            '⚠️ [relation.ts] getUserProfile key_attributes扁平化失败:',
            person.canonical_name,
            parseError,
          );
          person.key_attributes = {};
        }
      }

      // 处理relationships字段
      if (person.relationships && typeof person.relationships === 'string') {
        try {
          person.relationships = JSON.parse(person.relationships as string);
          console.log(
            '✅ [relation.ts] getUserProfile relationships JSON解析成功:',
            person.canonical_name,
            person.relationships,
          );
        } catch (parseError) {
          console.warn(
            '⚠️ [relation.ts] getUserProfile relationships JSON解析失败:',
            person.canonical_name,
            parseError,
          );
          person.relationships = [];
        }
      }
    }

    return response;
  } catch (error) {
    console.error('❌ [relation.ts] 获取用户档案失败:', error);
    throw error;
  }
};
