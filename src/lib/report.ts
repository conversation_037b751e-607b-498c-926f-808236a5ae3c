const isDev = process.env.VUE_APP_ENV !== 'production';

export enum ICategoryType {
  JS_ERROR = 'jsError',
  AJAX_ERROR = 'ajaxError',
  UN_KNOWN = 'un_known',
  BUSINESS_ERROR = 'business_error',
}

export enum ICustomMetricsName {
  IMG_UPLOAD_SUCCESS_RATE = 'img_upload_success_rate',
  WORKFLOW_RENDER_ERROR = 'workflow_render_error',
}

interface IReportErrorParams {
  name: string;
  msg?: string;
  category?: string;
  level?: string;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  tags?: Record<string, any>;
}

interface IReportCustomMetricsParams {
  name: string;
  value: number;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  tags?: Record<string, any>;
  isSuccess?: boolean;
}

/**
 * @description 上报页面报错信息
 * @param {String} name 错误名称：对应 raptor 异常列表中的"异常"一列，以及高级筛选中的"异常"项
 * @param {String} msg 错误日志内容：对应 raptor 异常详情页的"堆栈信息"；作为 name 的兜底内容
 * @param {String} category 错误类型：jsError, resourceError, ajaxError，默认 jsError；对应 raptor 分类筛选中的"异常类型"
 * @param {String} level 错误级别：error、warn、info、debug，默认error；对应 raptor 分类筛选中的"异常等级"
 * @param {Object} tags 其他用户自定义信息：对应 raptor 异常详情页的“自定义信息”
 */
export function reportError({
  name,
  msg = '',
  category = 'jsError',
  level = 'error',
  tags = {},
}: IReportErrorParams) {
  if (!name && !msg) {
    return;
  }
  // 增加公共 error tag：os （操作系统 Android、IOS）、appName（app 名称）、host（域名）
  const computedTags = {
    host: window.location.href,
    ...tags,
  };

  window.owl(
    'addError',
    {
      name: name || msg,
      msg,
    },
    {
      level,
      category,
      tags: computedTags,
    },
  );
}

/**
 * JS异常上报
 * @param {String} name 错误名称：对应 raptor 异常列表中的"异常"一列，以及高级筛选中的"异常"项
 * @param {String} msg 错误日志内容：对应 raptor 异常详情页的"堆栈信息"；作为 name 的兜底内容
 * @param {String} level 错误级别：error、warn、info、debug，默认error；对应 raptor 分类筛选中的"异常等级"
 * @param {Object} tags 其他用户自定义信息：对应 raptor 异常详情页的“自定义信息”
 */
export function reportJsError({ name, msg = '', level = 'error', tags = {} }: IReportErrorParams) {
  reportError({
    name,
    msg,
    category: ICategoryType.JS_ERROR,
    level,
    tags,
  });
}

/**
 * Ajax异常上报
 * @param {String} name 错误名称：对应 raptor 异常列表中的"异常"一列，以及高级筛选中的"异常"项
 * @param {String} msg 错误日志内容：对应 raptor 异常详情页的"堆栈信息"；作为 name 的兜底内容
 * @param {String} level 错误级别：error、warn、info、debug，默认error；对应 raptor 分类筛选中的"异常等级"
 * @param {Object} tags 其他用户自定义信息：对应 raptor 异常详情页的“自定义信息”
 */
export function reportAjaxError({
  name,
  msg = '',
  level = 'error',
  tags = {},
}: IReportErrorParams) {
  reportError({
    name,
    msg,
    category: ICategoryType.AJAX_ERROR,
    level,
    tags,
  });
}

/**
 * 自定义异常上报
 * @param {String} name 错误名称：对应 raptor 异常列表中的"异常"一列，以及高级筛选中的"异常"项
 * @param {String} msg 错误日志内容：对应 raptor 异常详情页的"堆栈信息"；作为 name 的兜底内容
 * @param {String} category 错误类型：jsError, resourceError, ajaxError，默认 jsError；对应 raptor 分类筛选中的"异常类型"
 * @param {String} level 错误级别：error、warn、info、debug，默认error；对应 raptor 分类筛选中的"异常等级"
 * @param {Object} tags 其他用户自定义信息：对应 raptor 异常详情页的“自定义信息”
 */
export function reportCustomError({
  name,
  msg = '',
  category = '',
  level = 'error',
  tags = {},
}: IReportErrorParams) {
  if (!category && isDev) {
    console.error('请填写错误类型');
    return;
  }

  const cate = category || ICategoryType.UN_KNOWN;
  reportError({
    name,
    msg,
    category: cate,
    level,
    tags,
  });
}

/**
 * @description 自定义指标上报
 * @param {String} name 自定义指标名称
 * @param {Boolean} isSuccess false——0---执行失败，true——100---执行成功，通过指标均值即可得到执行成功率
 * @param {Number} value 指标取值，赋值value字段后，会覆盖 isSuccess 段的设置
 * @param {Object} tags 自定义tag
 */
export function reportMetric({
  name,
  value = 100,
  isSuccess = true,
  tags = {},
}: IReportCustomMetricsParams) {
  if (!name) {
    return;
  }
  const metricInst = window.owl('newMetricInst');
  if (!value && value !== 0) {
    value = isSuccess ? 100 : 0;
  }
  //  设定指标维度
  // eslint-disable-next-line @typescript-eslint/no-unused-expressions
  metricInst &&
    metricInst.setTags({
      path: window.location.pathname,
      host: window.location.host,
      ...tags,
      result: isSuccess ? 'success' : 'fail',
    });
  // eslint-disable-next-line @typescript-eslint/no-unused-expressions
  metricInst && metricInst.setMetric(name, value);
}
