/* eslint-disable no-restricted-globals */
/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable @typescript-eslint/no-unused-expressions */
import Config from '@/config/index';

window.owl('start', {
  project: 'com.sankuai.friday.xiaomeivv.fe',
  pageUrl: location.href,
  devMode: Config.env !== 'product',
  page: {
    sample: 1,
    fstPerfAnalysis: true,
    logSlowView: true,
  },
  autoCatch: {
    pv: true,
    js: true,
    fetch: true,
    console: true,
  },
  ajax: {
    flag: true,
    duration: 2000,
  },
  image: {
    flag: true,
    duration: 5000,
    fileSize: 2 * 1024, // 2M
  },
  SPA: {
    autoPV: true,
  },
  enableLogTrace: true,
  logan: {
    enable: true, // 开启Logan，允许前端日志采集以及异常发生时的前置上报
    config: {
      devMode: Config.env !== 'product',
    },
  },
  resource: {
    enableStatusCheck: true,
    sampleApi: 1,
  },
  error: {
    formatUnhandledRejection: true,
  },
  ignoreList: {
    js: ['ResizeObserver loop limit exceeded', '[unhandledrejection] The user aborted a request'],
    ajax: ['https?://dreport.meituan.net', 'https://lx1.meituan.net'],
  },
  onBatchPush(instance: any) {
    if (
      instance &&
      instance.firstCategory === 'ajaxError' &&
      instance.logContent === 'from: xhr abort'
    ) {
      return undefined;
    }
    return instance;
  },
});
