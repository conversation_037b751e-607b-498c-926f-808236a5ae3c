// 皮肤主题配置文件
// 定义12个固定主题，对应ThemePanel中的主题色系

// 导入背景图片
import pinkImg from '@/assets/img/pink.jpg';
import yellowImg from '@/assets/img/yellow.jpg';
import blueImg from '@/assets/img/blue.jpg';

export interface IThemeColors {
  // 主色调
  primaryColor: string;
  primaryColorLight: string;
  primaryColorMedium: string;
  primaryColorStrong: string;
  primaryColorTimeStamp: string;

  // 强调色
  accentColor: string;
  accentColorLight: string;
  accentColorMedium: string;
  accentColorStrong: string;

  // 背景色
  bgGlass: string;
  bgGlassHover: string;
  bgGlassPopup: string;
  borderGlass: string;
  borderAccent: string;

  // 阴影
  shadowSoft: string;
  shadowStrong: string;
  shadowAccent: string;

  // 其他颜色
  overlayDark: string;
  bgInput: string;
  bgInputFocus: string;
  placeholderColor: string;
  successColor: string;
  successColorLight: string;
  successColorMedium: string;
  successColorStrong: string;
  errorColor: string;
  errorColorLight: string;
  errorColorMedium: string;
  errorColorStrong: string;

  // 页面文字颜色
  pageTextPrimary: string;
  pageTextSecondary: string;
  pageTextTertiary: string;

  // 主色背景上的文本颜色
  onPrimaryText: string;

  // PersonDetail组件专用文字颜色
  personDetailTitle: string;
  personDetailTimestamp: string;
  personDetailContext: string;

  // ChatItem组件专用文字颜色
  chatItemUserContext: string;
  chatItemAssistantContext: string;
  chatItemPreResponse: string;
}

export interface IThemeConfig {
  id: string;
  name: string;
  backgroundImage: string;
  colors: IThemeColors;
}

// 工具函数：生成不同透明度的 rgba
function rgba(hex: string, alpha: number) {
  const cleaned = hex.replace('#', '');
  const full = cleaned.length === 3
    ? cleaned.split('').map(c => c + c).join('')
    : cleaned;
  const r = parseInt(full.slice(0, 2), 16);
  const g = parseInt(full.slice(2, 4), 16);
  const b = parseInt(full.slice(4, 6), 16);
  return `rgba(${r}, ${g}, ${b}, ${alpha})`;
}

// 工具函数：生成比原色更浅的颜色
function lightenColor(hex: string, amount: number) {
  const cleaned = hex.replace('#', '');
  const full = cleaned.length === 3
    ? cleaned.split('').map(c => c + c).join('')
    : cleaned;
  const r = parseInt(full.slice(0, 2), 16);
  const g = parseInt(full.slice(2, 4), 16);
  const b = parseInt(full.slice(4, 6), 16);

  // 增加亮度，但不超过255
  const newR = Math.min(255, Math.round(r + (255 - r) * amount));
  const newG = Math.min(255, Math.round(g + (255 - g) * amount));
  const newB = Math.min(255, Math.round(b + (255 - b) * amount));

  return `#${newR.toString(16).padStart(2, '0')}${newG.toString(16).padStart(2, '0')}${newB.toString(16).padStart(2, '0')}`;
}

// 计算主色在何种文字颜色下对比度更好（始终返回黑色）
function onColorFor(_hex: string) {
  // 不管主色是什么颜色，都返回黑色
  return '#0b0f14';
}

// 创建主题颜色配置的工具函数
function createThemeColors(baseColor: string, isDark: boolean = false): IThemeColors {
  const accent = baseColor; // 强调色使用相同颜色
  
  return {
    // 主色调
    primaryColor: baseColor,
    primaryColorLight: rgba(baseColor, 0.1),
    primaryColorMedium: rgba(baseColor, 0.2),
    primaryColorStrong: rgba(baseColor, 0.3),
    primaryColorTimeStamp: rgba(baseColor, 0.8),

    // 强调色
    accentColor: accent,
    accentColorLight: rgba(accent, 0.1),
    accentColorMedium: rgba(accent, 0.2),
    accentColorStrong: rgba(accent, 0.3),

    // 背景色
    bgGlass: isDark ? rgba(baseColor, 0.15) : rgba(baseColor, 0.08),
    bgGlassHover: isDark ? rgba(baseColor, 0.25) : rgba(baseColor, 0.12),
    bgGlassPopup: isDark ? rgba(lightenColor(baseColor, 0.6), 0.92) : rgba(lightenColor(baseColor, 0.7), 0.88), // 基于比主题色更浅颜色的更低透明度
    borderGlass: isDark ? 'rgba(255, 255, 255, 0.2)' : rgba(baseColor, 0.2),
    borderAccent: rgba(accent, 0.3),

    // 阴影
    shadowSoft: '0 8px 24px rgba(0,0,0,0.12)',
    shadowStrong: '0 20px 60px rgba(0,0,0,0.28)',
    shadowAccent: `0 0 20px ${rgba(baseColor, 0.25)}`,

    // 其他颜色
    overlayDark: isDark ? rgba(baseColor, 0.2) : rgba(baseColor, 0.1),
    bgInput: isDark ? 'rgba(255, 255, 255, 0.08)' : rgba(baseColor, 0.05),
    bgInputFocus: isDark ? 'rgba(255, 255, 255, 0.15)' : rgba(baseColor, 0.1),
    placeholderColor: isDark ? 'rgba(255, 255, 255, 0.6)' : '#6b7280',
    
    // 语义颜色
    successColor: '#22c55e',
    successColorLight: 'rgba(34, 197, 94, 0.1)',
    successColorMedium: 'rgba(34, 197, 94, 0.2)',
    successColorStrong: 'rgba(34, 197, 94, 0.3)',
    errorColor: '#ef4444',
    errorColorLight: 'rgba(239, 68, 68, 0.1)',
    errorColorMedium: 'rgba(239, 68, 68, 0.2)',
    errorColorStrong: 'rgba(239, 68, 68, 0.3)',

    // 页面文字颜色
    pageTextPrimary: isDark ? '#ffffff' : '#374151',
    pageTextSecondary: isDark ? 'rgba(255,255,255,0.92)' : '#6b7280',
    pageTextTertiary: isDark ? 'rgba(255,255,255,0.8)' : '#9ca3af',

    // 主色背景上的文本颜色
    onPrimaryText: onColorFor(baseColor),

    // PersonDetail组件专用文字颜色
    personDetailTitle: accent,
    personDetailTimestamp: isDark ? 'rgba(255, 255, 255, 0.7)' : '#6b7280',
    personDetailContext: isDark ? 'rgba(255, 255, 255, 0.9)' : '#374151',

    // ChatItem组件专用文字颜色
    chatItemUserContext: isDark ? '#ffffff' : '#374151',
    chatItemAssistantContext: isDark ? 'rgba(255, 255, 255, 0.95)' : '#4b5563',
    chatItemPreResponse: rgba(accent, 0.8),
  };
}

// 12个固定主题，对应ThemePanel中的主题色系

// 1. 温暖橙调
export const warmOrangeTheme: IThemeConfig = {
  id: 'warm-orange',
  name: '温暖橙调',
  backgroundImage: '',
  colors: createThemeColors('#FF7A00'),
};

// 2. 清新蓝调 (保留原blue主题的配色)
export const freshBlueTheme: IThemeConfig = {
  id: 'fresh-blue',
  name: '清新蓝调',
  backgroundImage: blueImg,
  colors: createThemeColors('#1976D2'),
};

// 3. 自然绿调
export const naturalGreenTheme: IThemeConfig = {
  id: 'natural-green',
  name: '自然绿调',
  backgroundImage: '',
  colors: createThemeColors('#1B5E20'),
};

// 4. 优雅紫调
export const elegantPurpleTheme: IThemeConfig = {
  id: 'elegant-purple',
  name: '优雅紫调',
  backgroundImage: '',
  colors: createThemeColors('#7E5BEF', true),
};

// 5. 晚霞粉调 (保留原pink主题的配色)
export const dawnPinkTheme: IThemeConfig = {
  id: 'dawn-pink',
  name: '晚霞粉调',
  backgroundImage: pinkImg,
  colors: createThemeColors('#FF2D95'),
};

// 6. 深海青调
export const deepCyanTheme: IThemeConfig = {
  id: 'deep-cyan',
  name: '深海青调',
  backgroundImage: '',
  colors: createThemeColors('#00B5A9', true),
};

// 7. 樱花粉调
export const sakuraPinkTheme: IThemeConfig = {
  id: 'sakura-pink',
  name: '樱花粉调',
  backgroundImage: '',
  colors: createThemeColors('#E91E63'),
};

// 8. 森林深绿
export const forestGreenTheme: IThemeConfig = {
  id: 'forest-green',
  name: '森林深绿',
  backgroundImage: '',
  colors: createThemeColors('#1E7A46', true),
};

// 9. 薰衣草紫
export const lavenderTheme: IThemeConfig = {
  id: 'lavender',
  name: '薰衣草紫',
  backgroundImage: '',
  colors: createThemeColors('#B388FF'),
};

// 10. 秋叶金黄 (保留原yellow主题的配色)
export const autumnGoldTheme: IThemeConfig = {
  id: 'autumn-gold',
  name: '秋叶金黄',
  backgroundImage: yellowImg,
  colors: createThemeColors('#F57C00'),
};

// 11. 午夜蓝调
export const midnightBlueTheme: IThemeConfig = {
  id: 'midnight-blue',
  name: '午夜蓝调',
  backgroundImage: '',
  colors: createThemeColors('#274690', true),
};

// 12. 琥珀橘调
export const amberTheme: IThemeConfig = {
  id: 'amber',
  name: '琥珀橘调',
  backgroundImage: '',
  colors: createThemeColors('#FF3D00'),
};

// 所有主题列表
export const allThemes: IThemeConfig[] = [
  warmOrangeTheme,
  freshBlueTheme,
  naturalGreenTheme,
  elegantPurpleTheme,
  dawnPinkTheme,
  deepCyanTheme,
  sakuraPinkTheme,
  forestGreenTheme,
  lavenderTheme,
  autumnGoldTheme,
  midnightBlueTheme,
  amberTheme,
];

// 可用主题（所有主题都可用）
export const availableThemes: IThemeConfig[] = allThemes;

// 默认主题（使用清新蓝调）
export const defaultTheme: IThemeConfig = freshBlueTheme;

// 根据ID获取主题
export function getThemeById(id: string): IThemeConfig | undefined {
  return allThemes.find((theme) => theme.id === id);
}

// 获取默认主题
export function getDefaultTheme(): IThemeConfig {
  return defaultTheme;
}
