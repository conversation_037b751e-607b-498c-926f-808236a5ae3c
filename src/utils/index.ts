import { showSuccessToast, showFailToast } from 'vant';
import { sampleSize } from 'lodash-es';

export function copyToClipboard(str: string) {
  navigator.clipboard
    .writeText(str)
    .then(() => {
      showSuccessToast('复制成功');
    })
    .catch((err) => {
      showFailToast('复制失败');
    });
}
// 随机字符串
export function generateRandomString(length: number) {
  return sampleSize('ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789', length).join(
    '',
  );
}

// 格式化日期
export function formatDate(date: Date, format: string): string {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  const hours = String(date.getHours()).padStart(2, '0');
  const minutes = String(date.getMinutes()).padStart(2, '0');
  const seconds = String(date.getSeconds()).padStart(2, '0');

  return format
    .replace('YYYY', String(year))
    .replace('MM', month)
    .replace('DD', day)
    .replace('HH', hours)
    .replace('mm', minutes)
    .replace('ss', seconds);
}
