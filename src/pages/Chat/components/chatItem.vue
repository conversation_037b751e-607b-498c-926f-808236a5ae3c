<template>
  <div class="chat-message" :class="{ 'is-user': messageData.role === 'user' }">
    <!-- 调试信息 -->
    <div v-if="false" style="font-size: 12px; color: #999">
      <pre>{{ JSON.stringify(messageData, null, 2) }}</pre>
    </div>
    <div>
      <LoadingTip v-if="messageData.isToolCallLoading && messageData.role === 'assistant'" />
    </div>
    <div class="message-container">
      <!-- 消息内容上方展示AI推理依据 -->
      <PretoolsCard
        v-if="
          messageData.role === 'assistant' && messageData.pre_tools && messageData.pre_tools.length
        "
        :pretools="messageData.pre_tools"
      />
      <div
        class="message-content"
        :class="{
          'loading-content': !messageData.isFinish && messageData.role === 'assistant',
        }"
      >
        <template
          v-if="
            messageData.isFinish ||
            messageData.content ||
            messageData.reasoningData.content ||
            messageData.preResponseContent
          "
        >
          <template v-if="messageData.role === 'assistant'">
            <DeepThinking
              v-if="messageData.reasoningData.content"
              :reasoning-data="messageData.reasoningData"
            ></DeepThinking>
            <!-- 预响应内容显示 -->
            <div
              v-if="messageData.preResponseContent && !messageData.content"
              class="pre-response-content"
            >
              <messageRender :text="messageData.preResponseContent"></messageRender>
            </div>
            <!-- 正式回答内容显示 -->
            <messageRender v-if="messageData.content" :text="messageData.content"></messageRender>
          </template>
          <span v-else class="user-message-content">{{ messageData.content }}</span>
        </template>
        <div v-else-if="!messageData.isFinish && messageData.role === 'assistant'" class="loading">
          <span class="loading-dots">
            <span class="dot dot1">.</span>
            <span class="dot dot2">.</span>
            <span class="dot dot3">.</span>
          </span>
        </div>
        <!-- 操作按钮 (仅在 role=assistant 时显示) -->
        <div
          v-if="messageData.isFinish && messageData.role === 'assistant' && messageData.content"
          class="action-buttons"
        >
          <div class="action-btn" @click="handlePlay">
            <i
              v-if="
                !isCurrentAudioPlaying(messageData.debugInfo?.id || '') ||
                audioStatus === 'completed'
              "
              class="iconfont icon-roo-sqt-laba"
            ></i>
            <template v-else>
              <i v-if="audioStatus === 'loading'" class="iconfont icon-sg-loading"></i>
              <img v-else class="kaiqi-img" src="@/assets/img/langdu_kaiqi.png" alt="" />
            </template>
          </div>
          <div class="action-btn" @click="handleCopy">
            <i class="iconfont icon-mtdui-copy-o"></i>
          </div>
          <div v-if="isRegenerate" class="action-btn" @click="handleRegenerate">
            <i class="iconfont icon-sg-refresh"></i>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { defineProps, defineEmits, watch } from 'vue';
import messageRender from '@/components/Chat/messageRender.vue';
import { copyToClipboard } from '@/utils';
import DeepThinking from './DeepThinking.vue';
import LoadingTip from './LoadingTip.vue';
import PretoolsCard from './PretoolsCard.vue';
import { useAudioQueue } from '../useAudioPlayer';

const { play, stop, isCurrentAudioPlaying, audioStatus } = useAudioQueue();

interface IProps {
  messageData: IChatStreamContent;
  isRegenerate: boolean;
}

const props = defineProps<IProps>();
const emit = defineEmits(['regenerate']);

// 使用 watch 监听 messageData 的变化，在数据更新后输出日志
watch(
  () => props.messageData,
  (newVal) => {
    console.log('消息数据更新:', newVal);
    console.log('是否有 pre_tools:', newVal?.pre_tools);
    console.log('pre_tools 类型:', newVal?.pre_tools ? typeof newVal.pre_tools : 'undefined');
    console.log('pre_tools 长度:', newVal?.pre_tools?.length);
  },
  { deep: true },
);
const handlePlay = () => {
  console.log(11);
  if (isCurrentAudioPlaying(props.messageData.debugInfo?.id || '')) {
    stop();
  } else {
    play({
      id: props.messageData.debugInfo?.id || '',
      text: props.messageData.content,
      type: 'manualPlay',
    });
  }
};
const handleRegenerate = () => {
  emit('regenerate', props.messageData);
};
const handleCopy = () => {
  copyToClipboard(props.messageData.content);
};
</script>

<style scoped lang="scss">
// 统一配色方案
:root {
  --primary-color: #00bcd4;
  --primary-color-light: rgba(0, 188, 212, 0.1);
  --primary-color-medium: rgba(0, 188, 212, 0.2);
  --primary-color-strong: rgba(0, 188, 212, 0.3);

  --accent-color: #00ffff;
  --accent-color-light: rgba(0, 255, 255, 0.1);
  --accent-color-medium: rgba(0, 255, 255, 0.2);
  --accent-color-strong: rgba(0, 255, 255, 0.3);

  --text-primary: #ffffff;
  --text-secondary: rgba(255, 255, 255, 0.9);
  --text-tertiary: rgba(255, 255, 255, 0.7);
  --text-disabled: rgba(255, 255, 255, 0.5);

  --bg-glass: rgba(30, 58, 138, 0.15);
  --bg-glass-hover: rgba(30, 58, 138, 0.25);
  --border-glass: rgba(255, 255, 255, 0.2);
  --border-accent: rgba(0, 255, 255, 0.3);

  --shadow-soft: 0 8px 32px rgba(0, 0, 0, 0.2);
  --shadow-strong: 0 20px 60px rgba(0, 0, 0, 0.3);
  --shadow-accent: 0 0 20px rgba(0, 255, 255, 0.2);

  --font-size-xl: 18px;
  --font-size-2xl: 20px;
  --font-size-3xl: 24px;
}
.chat-message {
  width: 100%;
  margin: 24px 0px;
  box-sizing: border-box;
  position: relative;

  .message-container {
    display: flex;
    flex-direction: column;

    .message-content {
      width: fit-content;
      max-width: 80%;
      padding: 20px;
      background: var(--bg-glass);
      border: 2px solid var(--border-accent);
      border-radius: 16px 16px 16px 4px;
      backdrop-filter: blur(20px);
      box-shadow: var(--shadow-strong), var(--shadow-accent);

      // 使用设计系统字体
      color: var(--chat-item-assistant-context) !important; // 使用ChatItem专用AI回复颜色
      font-size: var(--font-size-3xl);
      font-weight: 400;
      line-height: 1.5;

      // 修复文本选中样式
      ::selection {
        background-color: var(--primary-color-medium);
        color: var(--text-primary);
      }

      ::-moz-selection {
        background-color: var(--primary-color-medium);
        color: var(--text-primary);
      }

      // 确保所有子元素都使用正确的文字颜色
      * {
        color: inherit !important;
      }

      // 特别处理可能的黑色文字
      p,
      span,
      div,
      text {
        color: var(--chat-item-assistant-context) !important; // 使用ChatItem专用AI回复颜色
      }
    }

    // 预响应内容样式
    .pre-response-content {
      opacity: 0.7;
      font-style: italic;
      color: var(--chat-item-pre-response) !important; // 使用ChatItem专用预响应颜色

      * {
        color: var(--chat-item-pre-response) !important; // 使用ChatItem专用预响应颜色
      }
    }

    // 加载内容样式
    &.loading-content {
      border-radius: 16px 16px 16px 0px;
    }

    .loading {
      display: flex;
      align-items: center;
      justify-content: flex-start;
      padding: 0;
      min-height: auto;

      .loading-dots {
        display: inline-block;
        margin-left: 2px;

        .dot {
          display: inline-block;
          opacity: 0.3;
          animation: dotPulse 0.8s infinite;
          color: var(--accent-color);
          font-size: var(--font-size-3xl);
          font-weight: bold;
        }

        .dot1 {
          animation-delay: 0s;
        }

        .dot2 {
          animation-delay: 0.25s;
        }

        .dot3 {
          animation-delay: 0.5s;
        }
      }
    }

    // 用户消息内容样式
    .user-message-content {
      color: var(--chat-item-user-context) !important; // 使用ChatItem专用用户消息颜色
    }
  }

  &.is-user {
    margin-left: auto;

    .message-container {
      flex-direction: row-reverse;

      .message-content {
        font-weight: 400;
        color: var(--chat-item-user-context) !important; // 使用ChatItem专用用户消息颜色
        background: var(--bg-glass);
        border: 2px solid var(--primary-color);
        border-radius: 16px 16px 4px 16px;
        box-shadow:
          var(--shadow-strong),
          0 0 20px var(--primary-color-strong);
      }
    }
  }

  .action-buttons {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    gap: 24px;
    color: var(--text-tertiary);
    padding-top: 16px;

    .action-btn {
      width: 40px;
      height: 40px;
      display: flex;
      align-items: center;
      justify-content: center;
      background: var(--bg-glass);
      border: 1px solid var(--border-glass);
      border-radius: 50%;
      font-size: 24px;
      cursor: pointer;
      transition: all 0.3s ease;

      &:hover {
        background: var(--bg-glass-hover);
        border-color: var(--border-accent);
        transform: translateY(-2px);
        box-shadow: var(--shadow-accent);
      }

      .icon-sg-loading {
        color: var(--accent-color);
        animation: loadingSpin 2s linear infinite;
      }

      .kaiqi-img {
        width: 24px;
        height: auto;
        filter: brightness(0) invert(1);
      }
    }
  }
}

@keyframes loadingSpin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes dotPulse {
  0%,
  100% {
    opacity: 0.3;
  }
  50% {
    opacity: 1;
  }
}

// 响应式适配
@media (max-width: 768px) {
  .chat-message {
    margin: 16px 0px;

    .message-container {
      .message-content {
        max-width: 90%;
        padding: 16px;
        border-radius: 12px 12px 12px 4px;
        font-size: var(--font-size-3xl) !important;
        line-height: 1.5;
        color: var(--chat-item-assistant-context) !important; // 使用ChatItem专用AI回复颜色

        &.loading-content {
          border-radius: 12px 12px 12px 0px;
        }

        .loading {
          .loading-dots {
            .dot {
              font-size: var(--font-size-2xl);
            }
          }
        }
      }
    }

    &.is-user {
      .message-container {
        .message-content {
          border-radius: 12px 12px 4px 12px;
        }
      }
    }

    .action-buttons {
      gap: 16px;
      padding-top: 12px;

      .action-btn {
        width: 32px;
        height: 32px;
        font-size: 20px;

        .kaiqi-img {
          width: 20px;
        }
      }
    }
  }
}
</style>
