<template>
  <div class="v-chat-container theme-background">
    <!-- 弹幕组件 - 移到容器内部，确保在所有组件之上 -->
    <Barrage ref="barrageRef" />
    <div class="v-chat" style="position: relative">
      <!-- 头部区域改为Maintopbar组件 -->
      <Maintopbar
        :show-back-btn="false"
        :show-history-btn="true"
        :show-relationship-btn="true"
        :show-user-avatar="false"
        :show-assistant-avatar="true"
        :assistant-avatar-src="selectedAssistantAvatar"
        :assistant-name="selectedAssistantName"
        :selected-assistant-avatar="selectedAssistantAvatar"
        :show-voice-btn="false"
        :show-add-chat-btn="false"
        :show-weather-btn="shouldShowWeatherBtn"
        :show-memo-btn="true"
        :show-home-btn="true"
        :show-back-to-index-btn="true"
        :show-feature-intro="true"
        add-chat-type="add"
        :is-chat-play="isChatPlay"
        :user-loading="false"
        :current-mis-id="''"
        :get-random-color="() => '#ccc'"
        :get-avatar-letter="() => ''"
        :show-header-grad="false"
        @history="toggleHistorySidebar"
        @voice="handleChatPlay"
        @relationship="handleRelationship"
        @weather="handleWeather"
        @memo="handleMemo"
        @home="handleHome"
        @back-to-index="handleBackToIndex"
      />
      <!-- 中间内容区域 -->
      <div class="chat-content">
        <div ref="scrollWrapper" class="chat-scroll-wrapper" @scroll="checkScrollBottom">
          <!-- 悬浮信息卡片 -->
          <FloatingInfoCard
            :visible="showFloatingCard"
            :card-type="floatingCardType"
            :user-id="userStore.userInfo?.login"
            @close="handleCloseFloatingCard"
          />

          <template v-for="(item, index) in chatMessageList" :key="item.key">
            <ChatItem
              :message-data="item"
              :is-regenerate="index === chatMessageList.length - 1"
              @regenerate="handleRegenerate"
            ></ChatItem>
          </template>
        </div>
      </div>

      <!-- 底部区域：预设问题和输入框 -->
      <div class="footer">
        <!-- 预设问题组件 - 录音时隐藏 -->
        <PresetQuestions
          v-show="!isRecording"
          :questions="presetQuestions"
          @question-click="handlePresetQuestionClick"
        />

        <!-- 输入框 -->
        <form class="input-wrapper" action="" @submit.prevent="handleFormSubmit">
          <inputBar
            ref="inputBarRef"
            @voice-send="handleInputSend"
            @get-started="handleInputSend"
            @send="handleInputSend"
            @stop="handleStop"
            @recording-status="handleRecordingStatus"
          />
        </form>

        <!-- 老董假装说话样式 - 直接放在输入框下方 -->
        <div class="laodong-fake-speaking">
          <div class="fake-speaking-container">
            <div class="laodong-avatar">
              <img :src="selectedAssistantAvatar" alt="老董头像" />
            </div>
            <div class="fake-speaking-content">
              <div class="fake-speaking-text">老董会根据您的问题给出专业建议</div>
              <div class="fake-speaking-dots">
                <span class="dot"></span>
                <span class="dot"></span>
                <span class="dot"></span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 历史对话侧边栏 -->
      <HistorySidebar
        :is-open="showHistorySidebar"
        :current-title="currentTitle"
        @close="showHistorySidebar = false"
        @select-conversation="handleSelectConversation"
      />

      <!-- 备忘录侧边栏 -->
      <MemoSidebar :is-open="showMemoSidebar" @close="showMemoSidebar = false" />

      <!-- 天气侧边栏 -->
      <WeatherSidebar :is-open="showWeatherSidebar" @close="showWeatherSidebar = false" />
    </div>
  </div>
</template>

<script setup lang="ts">
import Maintopbar from '@/components/Maintopbar.vue';
import { ref, nextTick, onBeforeMount, onMounted, onUnmounted, watch, computed } from 'vue';
import { AnswerStatusEnum, ReasoningStatus } from '@/constants/chat';

import { useChatStore } from '@/stores/chat';
import { useUserStore } from '@/stores/user';
import { useThemeStore } from '@/stores/theme';
import { showSuccessToast, showFailToast } from 'vant';
import { Typewriter } from '@/utils/typeWriter';
import { debounce } from 'lodash-es';
import { generateRandomString } from '@/utils';
import HistorySidebar from '@/components/Chat/historySidebar.vue';
import { getUserInfo } from '@/apis/common';
import { streamChat, createConversation, type IToolCall } from '@/apis/chat';

import { refreshWeatherAfterChat } from '@/utils/weatherRefresh';
import inputBar from '@/components/Chat/inputBar.vue';
import { useRoute, useRouter } from 'vue-router';

import PresetQuestions from '@/components/Chat/PresetQuestions.vue';
import MemoSidebar from '@/components/Chat/memoSidebar.vue';
import WeatherSidebar from '@/components/Chat/weatherSidebar.vue';

import FloatingInfoCard from '@/components/Chat/FloatingInfoCard.vue';
import Barrage from '@/components/Chat/Barrage.vue';
import { getConversationChanges } from '@/apis/memo';
// 导入头像图片
import avatar1 from '@/assets/icon/laodong1.jpg';
import avatar2 from '@/assets/icon/laodong2.jpg';
import avatar3 from '@/assets/icon/laodong3.png';
import avatar4 from '@/assets/icon/laodong4.png';
import avatar5 from '@/assets/icon/laodong5.png';
import avatar6 from '@/assets/icon/laodong6.jpg';
import ChatItem from './components/chatItem.vue';
import { useAudioQueue } from './useAudioPlayer';

// 历史会话中的消息接口
interface IHistoryMessage {
  role: string;
  content: string;
  reasoning_content?: string;
}

// 历史会话数据接口
interface IConversationData {
  conversationId: string;
  title?: string;
  messageList?: IHistoryMessage[];
}

const { stop, isPlaying } = useAudioQueue();

const typewriter = new Typewriter(
  async (str: string) => {
    if (str) {
      console.log('🖨️ [chat.vue] typewriter更新消息内容:', {
        displayLength: str.length,
        preview: str.substring(0, 50) + (str.length > 50 ? '...' : ''),
        messageIndex: chatMessageList.value.length - 1,
        isStoppedByUser: isStoppedByUser.value,
      });

      // 添加安全检查，确保消息索引有效
      if (chatMessageList.value.length > 0) {
        const lastMessage = chatMessageList.value[chatMessageList.value.length - 1];
        if (lastMessage && lastMessage.role === 'assistant') {
          lastMessage.content = str;
          await nextTick(() => {
            scrollToEnd();
          });
        } else {
          console.warn('⚠️ [chat.vue] typewriter回调时最后一条消息不是助手消息');
        }
      } else {
        console.warn('⚠️ [chat.vue] typewriter回调时消息列表为空');
      }
    }
  },
  () => {
    // 打字机自动完成回调
    console.log('✅ [chat.vue] typewriter自动完成回调触发');
    chatStore.setAnswerStatus(AnswerStatusEnum.SUCCESS);
    isTypewriterStarted.value = false;
    // 打字机完成时，允许发送新消息
    canSendMessage.value = true;
    streamController.value = null;
    // 回答完毕时，检查是否新增了备忘录或者懂量提升，有则通过弹幕显示
    checkMemoUpdates()
      .then((result) => {
        if (result.hasUpdate && result.data && barrageRef.value) {
          console.log('🎯 [chat.vue] 检测到备忘录更新，显示弹幕:', result.data);
          barrageRef.value.showBarrage(result.data.type, result.data.content);
        } else {
          console.log('🎯 [chat.vue] 没有检测到备忘录更新');
        }
      })
      .catch((error) => {
        console.error('❌ [chat.vue] 检查备忘录更新失败:', error);
      });

    // 聊天完成后刷新天气数据
    if (currentMisId.value && currentMisId.value !== 'unknown_user') {
      refreshWeatherAfterChat(currentMisId.value)
        .then(() => {
          console.log('✅ [chat.vue] 聊天完成后天气数据刷新成功');
        })
        .catch((error) => {
          console.error('❌ [chat.vue] 聊天完成后天气数据刷新失败:', error);
        });
    }
  },
);
const reasoningTypewriter = new Typewriter(async (str: string) => {
  if (str) {
    chatMessageList.value[chatMessageList.value.length - 1].reasoningData.content = str;
    await nextTick(() => {
      scrollToEnd();
    });
  }
});

// 添加标志位来跟踪打字机是否已启动
const isTypewriterStarted = ref(false);

// 添加发送消息的标志变量，控制是否可以发送新消息
const canSendMessage = ref(true);

const route = useRoute();
const router = useRouter();
const chatStore = useChatStore();
const userStore = useUserStore();
const themeStore = useThemeStore();
const scrollWrapper = ref();
const isScrollDown = ref(false);
const isChatPlay = ref(false);
const message = ref('');

const inputBarRef = ref<InstanceType<typeof inputBar> | null>(null);
const barrageRef = ref<InstanceType<typeof Barrage> | null>(null);
const conversationId = ref('');
const chatMessageList = ref<IChatStreamContent[]>([]);

// 历史侧边栏相关
const showHistorySidebar = ref(false);

// 备忘录侧边栏相关
const showMemoSidebar = ref(false);

// 天气侧边栏相关
const showWeatherSidebar = ref(false);

// 工具调用状态管理
const activeToolCalls = ref<Map<string, IToolCall>>(new Map());

// 录音状态管理
const isRecording = ref(false);

// 悬浮卡片相关状态
const showFloatingCard = ref(false);
const floatingCardType = ref<'weather' | 'record' | null>(null);

// 预设问题数据
const presetQuestions = ref([
  '明天的天气怎么样？',
  '怎么提高工作效率？',
  '有什么好玩的地方吗？',
  '什么时候适合联系？',
]);

// 当前标题计算属性
const currentTitle = computed(() => {
  return (route.params.title as string) || '';
});

// 是否显示天气按钮的计算属性
const shouldShowWeatherBtn = computed(() => {
  const cardType = route.query.cardType as string;
  const chatFeature = sessionStorage.getItem('chatFeature');

  // 当cardType为'record'时（了解您的情况）或cardType为'question'时（懂问题），不显示天气按钮
  // 同时检查sessionStorage中的chatFeature
  return cardType !== 'record' && cardType !== 'question' && chatFeature !== 'question';
});

// 停止生成状态
const isStoppedByUser = ref(false);

// 思考模式状态标志
const isInThinkingMode = ref(false);

// 流式请求控制器
const streamController = ref<AbortController | null>(null);

// 注意：原来的思考内容构建状态变量已移除，因为现在通过独立的reflection类型处理思考内容

// 用户信息
const currentMisId = ref('');

// AI助理头像选择的存储键
const AI_ASSISTANT_STORAGE_KEY = 'selectedAssistantIndex';

// AI助手数据
const assistants = ref([
  {
    id: 1,
    name: '老董',
    avatar: avatar1,
  },
  {
    id: 2,
    name: '老董',
    avatar: avatar2,
  },
  {
    id: 3,
    name: '老董',
    avatar: avatar3,
  },
  {
    id: 4,
    name: '老董',
    avatar: avatar4,
  },
  {
    id: 5,
    name: '老董',
    avatar: avatar5,
  },
  {
    id: 6,
    name: '老董',
    avatar: avatar6,
  },
]);

// 立即从localStorage获取初始索引，避免跳变
const getInitialAssistantIndex = (): number => {
  const savedIndex = localStorage.getItem(AI_ASSISTANT_STORAGE_KEY);
  const index = savedIndex ? parseInt(savedIndex, 10) : 0;
  console.log('🔍 [chat.vue] 获取初始助手索引:', { savedIndex, index });
  if (index >= 0 && index < 6) {
    // 助手数组长度为6
    console.log('✅ [chat.vue] 使用保存的助手索引:', index);
    return index;
  }
  console.log('⚠️ [chat.vue] 使用默认助手索引: 0');
  return 0;
};

// 响应式的助手索引，立即初始化为正确值
const currentAssistantIndex = ref(getInitialAssistantIndex());

// 获取选中的助手头像
const selectedAssistantAvatar = computed(() => {
  const index = currentAssistantIndex.value;
  if (index >= 0 && index < assistants.value.length) {
    console.log(
      '✅ [chat.vue] 计算属性返回助手头像:',
      assistants.value[index].name,
      assistants.value[index].avatar,
    );
    return assistants.value[index].avatar;
  }
  console.log(
    '⚠️ [chat.vue] 计算属性返回默认助手头像:',
    assistants.value[0].name,
    assistants.value[0].avatar,
  );
  return assistants.value[0].avatar;
});

// 获取选中的助手名称
const selectedAssistantName = computed(() => {
  const index = currentAssistantIndex.value;
  if (index >= 0 && index < assistants.value.length) {
    return assistants.value[index].name;
  }
  return assistants.value[0].name;
});

// 标记是否已经发送过初始消息
const hasInitialMessageSent = ref(false);

// 检查备忘录更新的接口
const checkMemoUpdates = async (): Promise<{
  hasUpdate: boolean;
  data?: { type: 'memo' | 'knowledge'; content: string };
}> => {
  try {
    // 检查必要参数是否存在
    if (!currentMisId.value || !conversationId.value) {
      console.log('🔍 [chat.vue] 缺少必要参数，跳过备忘录更新检查');
      return { hasUpdate: false };
    }

    // 调用真实的API接口
    const result = await getConversationChanges(currentMisId.value, conversationId.value);
    console.log('🔍 [chat.vue] 备忘录更新检查结果:', result);

    return result;
  } catch (error) {
    console.error('❌ [chat.vue] 检查备忘录更新失败:', error);
    // 出错时返回无更新，避免影响正常聊天
    return { hasUpdate: false };
  }
};

const handleChatPlay = () => {
  isChatPlay.value = !isChatPlay.value;
  if (!isChatPlay.value) {
    stop();
  }
};

// 处理关系拓扑按钮点击
const handleRelationship = async () => {
  console.log('跳转到关系拓扑页面');

  // 记录来源页面为chat
  sessionStorage.setItem('relationGraphSource', 'chat');

  await router.push({
    name: 'relationship-graph',
  });
};

// 处理天气按钮点击
const handleWeather = () => {
  console.log('🔖 [chat.vue] 点击天气按钮，打开天气侧边栏');
  showWeatherSidebar.value = !showWeatherSidebar.value;
};

// 处理备忘录按钮点击
const handleMemo = () => {
  console.log('🔖 [chat.vue] 点击备忘录按钮，打开备忘录侧边栏');
  showMemoSidebar.value = !showMemoSidebar.value;
};

// 处理首页按钮点击
const handleHome = async () => {
  console.log('🏠 [chat.vue] 点击home按钮，跳转到index页面聊天界面');
  // 清理sessionStorage数据
  sessionStorage.removeItem('returnToPersonDetail');
  sessionStorage.removeItem('chatFeature'); // 清理功能标记
  // 设置从home按钮跳转的标记，让index页面直接显示聊天界面
  sessionStorage.setItem('fromHomeButton', 'true');
  // 重置初始消息发送标记
  hasInitialMessageSent.value = false;
  await router.push({
    name: 'chat', // 跳转到index页面
  });
};

// 处理回到首页按钮点击
const handleBackToIndex = async () => {
  console.log('🏠 [chat.vue] 点击回到首页按钮，跳转到index页面');
  // 清理sessionStorage数据
  sessionStorage.removeItem('returnToPersonDetail');
  sessionStorage.removeItem('chatFeature'); // 清理功能标记
  // 重置初始消息发送标记
  hasInitialMessageSent.value = false;
  await router.push({
    name: 'chat', // 跳转到index页面
  });
};

// 初始化新会话方法（不跳转，用于页面初始化）
const initNewChat = async () => {
  chatMessageList.value = []; // 清空消息列表

  // 创建新会话ID
  try {
    if (currentMisId.value && currentMisId.value !== 'unknown_user') {
      const response = await createConversation({
        user_id: currentMisId.value,
      });

      if (response.success && response.conversation_id) {
        conversationId.value = response.conversation_id;
        console.log('✅ [chat.vue] 初始化新会话成功，会话ID:', conversationId.value);
      } else {
        throw new Error('创建会话失败');
      }
    }
  } catch (error) {
    console.error('❌ [chat.vue] 初始化会话失败:', error);
    // 如果接口调用失败，回退到生成随机ID
    conversationId.value = generateRandomString(16);
    console.log('🔄 [chat.vue] 回退到生成随机会话ID:', conversationId.value);
  }

  // 重置思考模式标志
  isInThinkingMode.value = false;
  resetThinkingState();
  // 重置打字机启动标志
  isTypewriterStarted.value = false;
  // 重置发送消息标志，允许发送新消息
  canSendMessage.value = true;
  // 标记已初始化
  isInitialized.value = true;
  console.log('新会话已初始化');
};

// 终止会话 - 用于停止生成功能
const handleStop = () => {
  console.log('🛑 [chat.vue] 用户停止生成');

  // 设置用户停止标志，后续消息将被忽略显示
  isStoppedByUser.value = true;

  // 重置打字机启动标志
  isTypewriterStarted.value = false;

  // 用户停止时，允许发送新消息
  canSendMessage.value = true;

  // 取消流式请求
  if (streamController.value) {
    streamController.value.abort();
    streamController.value = null;
  }

  // 重置思考模式标志
  isInThinkingMode.value = false;
  resetThinkingState();

  // 停止打字机效果
  typewriter.done();
  reasoningTypewriter.done();

  // 标记最后一条助手消息为完成
  const lastAssistantMessage = chatMessageList.value[chatMessageList.value.length - 1];
  if (lastAssistantMessage && lastAssistantMessage.role === 'assistant') {
    lastAssistantMessage.isFinish = true;
    lastAssistantMessage.key = Date.now();

    // 如果有思考内容，标记为完成状态
    if (lastAssistantMessage.reasoningData && lastAssistantMessage.reasoningData.content) {
      lastAssistantMessage.reasoningData.status = ReasoningStatus.FINISHED.toString();
    }
  }

  // 停止音频播放
  if (isPlaying.value) {
    stop();
  }

  // 立即重置状态，允许用户继续输入
  chatStore.setAnswerStatus(AnswerStatusEnum.SUCCESS);
};

// 处理表单提交
const handleFormSubmit = () => {
  // 表单提交时不做任何操作，因为实际的发送逻辑由inputBar组件的send事件处理
  console.log('表单提交被阻止，使用组件事件处理');
};

// 处理预设问题点击
const handlePresetQuestionClick = (question: string) => {
  console.log('🎯 [chat.vue] 点击预设问题:', question);

  // 检查是否可以发送消息（打字机工作期间禁止发送）
  if (!canSendMessage.value) {
    console.log('🚫 [chat.vue] 打字机正在工作，禁止发送预设问题');
    showFailToast('请等待当前回复完成后再发送消息');
    return;
  }

  // 直接发送预设问题
  void handleInputSend(question);
};

// 处理录音状态变化
const handleRecordingStatus = (recordingStatus: boolean) => {
  console.log('🎤 [chat.vue] 录音状态变化:', recordingStatus);
  isRecording.value = recordingStatus;
};

// 处理悬浮卡片关闭
const handleCloseFloatingCard = () => {
  console.log('🔄 [chat.vue] 关闭悬浮卡片');
  showFloatingCard.value = false;
  floatingCardType.value = null;
};

// 发送消息
const handleInputSend = async (val?: string) => {
  const content = typeof val === 'string' ? val : message.value;
  if (!content.trim()) return;

  // 检查是否可以发送消息（打字机工作期间禁止发送）
  if (!canSendMessage.value) {
    console.log('🚫 [chat.vue] 打字机正在工作，禁止发送新消息');
    showFailToast('请等待当前回复完成后再发送消息');
    return;
  }

  console.log('🚀 [chat.vue] 开始发送消息:', content);

  // 检查用户信息是否已加载
  if (!currentMisId.value || currentMisId.value === 'unknown_user') {
    showFailToast('用户信息未加载，请刷新页面重试');
    return;
  }

  // 检查会话ID是否存在，如果不存在则创建新会话
  if (!conversationId.value) {
    try {
      const response = await createConversation({
        user_id: currentMisId.value,
      });

      if (response.success && response.conversation_id) {
        conversationId.value = response.conversation_id;
        console.log('✅ [chat.vue] 创建新会话成功，会话ID:', conversationId.value);
      } else {
        throw new Error('创建会话失败');
      }
    } catch (error) {
      console.error('❌ [chat.vue] 创建会话失败:', error);
      // 如果接口调用失败，回退到生成随机ID
      conversationId.value = generateRandomString(16);
      console.log('🔄 [chat.vue] 回退到生成随机会话ID:', conversationId.value);
    }
  }

  // 重置用户停止标志
  isStoppedByUser.value = false;

  // 重置打字机启动标志
  isTypewriterStarted.value = false;

  // 开始发送消息时，禁止发送新消息
  canSendMessage.value = false;

  // 添加用户消息到聊天列表
  chatMessageList.value.push({
    role: 'user',
    content,
    key: Date.now(),
    isFinish: true,
    reasoningData: {} as IReasoningData,
  });

  // 清空输入框
  message.value = '';

  // 设置加载状态
  chatStore.setAnswerStatus(AnswerStatusEnum.LOADING);

  // 添加助手消息占位符
  const assistantMessage: IChatStreamContent = {
    role: 'assistant',
    content: '',
    key: Date.now() + 1,
    isFinish: false,
    reasoningData: {} as IReasoningData,
    isToolCallLoading: false,
  };
  chatMessageList.value.push(assistantMessage);

  // 滚动到底部
  await nextTick(() => {
    scrollToEnd();
  });

  try {
    // 创建新的AbortController
    streamController.value = new AbortController();

    // 构建请求数据
    const requestData = getSendQuery(content);
    console.log('📤 [chat.vue] 发送请求数据:', requestData);

    // 开始流式聊天
    await streamChat(
      requestData,
      {
        onMessage: (messageContent: string, isFinal: boolean) => {
          if (isStoppedByUser.value) {
            console.log('🚫 [chat.vue] 用户已停止，忽略消息片段:', {
              contentLength: messageContent.length,
              preview: messageContent.substring(0, 30) + (messageContent.length > 30 ? '...' : ''),
            });
            return;
          }

          // 清空预响应内容（正式回答开始时）
          const lastMessage = chatMessageList.value[chatMessageList.value.length - 1];
          if (lastMessage && lastMessage.role === 'assistant' && lastMessage.preResponseContent) {
            console.log('🔄 [chat.vue] 清空预响应内容，开始正式回答');
            lastMessage.preResponseContent = '';
            lastMessage.key = Date.now(); // 触发响应式更新
          }

          // 统一处理：无论是否为最终消息，都先添加到打字机队列
          typewriter.add(messageContent);

          // 启动打字机（如果还未启动）
          if (!isTypewriterStarted.value) {
            console.log('🚀 [chat.vue] 启动typewriter，开始显示内容');
            isTypewriterStarted.value = true;
            typewriter.start();
          }

          // 如果是最终消息，标记打字机完成（让它自然消费完队列）
          if (isFinal) {
            console.log('🏁 [chat.vue] 收到isFinal消息，标记打字机完成');
            typewriter.markFinished();

            const finalMessage = chatMessageList.value[chatMessageList.value.length - 1];
            if (finalMessage && finalMessage.role === 'assistant') {
              console.log('📝 [chat.vue] 标记最后一条助手消息为完成:', {
                messageContent:
                  messageContent.substring(0, 50) + (messageContent.length > 50 ? '...' : ''),
                contentLength: messageContent.length,
                wasFinished: finalMessage.isFinish,
              });
              finalMessage.isFinish = true;
              finalMessage.key = Date.now(); // 触发响应式更新
            } else {
              console.warn('⚠️ [chat.vue] 收到isFinal消息时找不到有效的助手消息');
            }

            // 注意：不在这里立即调用完成回调，而是让打字机自然完成后触发
            // 完成回调将在打字机的 onComplete 中触发
          }
        },
        onPreResponse: (PreResponseContent: string, stage: string) => {
          if (isStoppedByUser.value) return;
          console.log('🔍 [chat.vue] 收到预响应内容:', PreResponseContent, stage);

          // 获取最后一条助手消息
          const lastMessage = chatMessageList.value[chatMessageList.value.length - 1];
          if (lastMessage && lastMessage.role === 'assistant') {
            // 累积预响应内容
            if (!lastMessage.preResponseContent) {
              // 第一个预响应内容
              lastMessage.preResponseContent = PreResponseContent;
            } else {
              // 追加后续预响应内容，用换行符分隔
              lastMessage.preResponseContent = `${lastMessage.preResponseContent}\n${PreResponseContent}`;
            }

            // 触发响应式更新
            lastMessage.key = Date.now();
            console.log('🔍 [chat.vue] 更新预响应内容:', lastMessage.preResponseContent);
          }
        },
        onToolCall: (toolCall: IToolCall) => {
          if (isStoppedByUser.value) return;
          console.log('🔧 [chat.vue] 工具调用:', toolCall);
          const lastMessage = chatMessageList.value[chatMessageList.value.length - 1];
          if (lastMessage && lastMessage.role === 'assistant') {
            if (toolCall.status === 'start') {
              // 工具调用开始，显示LoadingTip
              lastMessage.isToolCallLoading = true;
              lastMessage.key = Date.now(); // 触发响应式更新
              activeToolCalls.value.set(toolCall.tool_name, toolCall);
            } else if (toolCall.status === 'end') {
              // 工具调用结束，隐藏LoadingTip
              lastMessage.isToolCallLoading = false;
              lastMessage.key = Date.now(); // 触发响应式更新
              // 移除活跃的工具调用
              activeToolCalls.value.delete(toolCall.tool_name);

              // 处理添加提醒的工具调用
              if (toolCall.tool_name === 'add_reminder') {
                console.log('✅ [chat.vue] 检测到添加提醒工具调用完成');
                showSuccessToast('提醒添加成功！');
              }
            }
          }
        },
        onRecommendations: (recommendations: string[]) => {
          // 更新预设问题
          console.log('🎯 [chat.vue] 收到推荐问题:', recommendations);
          presetQuestions.value = recommendations;
        },
        onEnd: () => {
          console.log('不会触发到的onEnd回调');
        },
        onError: (error: Error) => {
          console.error('❌ [chat.vue] 流式聊天错误:', error);
          handleChatError(`发送消息失败: ${error.message}`);
          streamController.value = null;
        },
        onClose: () => {
          console.log('🔗 [chat.vue] 流式聊天连接已关闭');
        },
      },
      streamController.value.signal,
    );
  } catch (error) {
    console.error('❌ [chat.vue] 发送消息异常:', error);
    handleChatError(`发送消息失败: ${error instanceof Error ? error.message : '未知错误'}`);
    streamController.value = null;
  }
};

const handleChatError = (errorText: string, status: AnswerStatusEnum = AnswerStatusEnum.FAIL) => {
  console.log('聊天错误:', errorText);

  // 设置错误状态
  chatStore.setAnswerStatus(status);

  // 重置打字机启动标志
  isTypewriterStarted.value = false;

  // 发生错误时，允许发送新消息
  canSendMessage.value = true;

  // 重置思考模式标志
  isInThinkingMode.value = false;
  resetThinkingState();

  const lastMessage = chatMessageList.value[chatMessageList.value.length - 1];
  if (lastMessage && lastMessage.role === 'assistant') {
    lastMessage.isFinish = true;

    // 修复：如果消息已经有内容（typewriter显示的内容），不要直接覆盖
    // 而是添加错误信息作为后缀，或者根据情况决定是否覆盖
    if (!lastMessage.content || lastMessage.content.trim() === '') {
      // 如果没有内容，直接设置错误文本
      lastMessage.content = errorText;
    } else if (errorText.includes('连接') || errorText.includes('网络')) {
      // 如果已有内容且是连接相关错误，保留已有内容
      console.log('🔗 [chat.vue] 连接相关错误，保留已有内容:', lastMessage.content);
      // 保持原有内容，只标记为完成
    } else {
      // 如果已有内容且是真正的错误，添加错误信息
      lastMessage.content += `\n\n${errorText}`;
    }

    lastMessage.key = Date.now();
  } else {
    chatMessageList.value.push({
      role: 'assistant',
      content: errorText,
      key: Date.now(),
      isFinish: true,
      reasoningData: {} as IReasoningData,
    });
  }

  // 修复：优雅地结束typewriter而不是强制done
  // 如果有正在进行的内容，让它自然完成
  typewriter.markFinished();
  // 给一个短暂延迟让typewriter完成，然后清理
  setTimeout(() => {
    typewriter.done();
    reasoningTypewriter.done();
  }, 100);

  // 延迟重置状态，允许下次输入
  setTimeout(() => {
    chatStore.setAnswerStatus(AnswerStatusEnum.SUCCESS);
  }, 1000);

  void nextTick(() => {
    scrollToEnd();
  });
};

// 重置思考相关状态（简化版本）
const resetThinkingState = () => {
  console.log('重置思考状态');
};

// 构建聊天请求数据
const getSendQuery = (content: string): IChatRequest => {
  return {
    content,
    conversation_id: conversationId.value,
    user_id: currentMisId.value,
  };
};

const scrollToEnd = debounce(() => {
  if (scrollWrapper.value) {
    scrollWrapper.value.scrollTo({
      top: scrollWrapper.value.scrollHeight,
      behavior: 'smooth',
    });
  }
}, 300);

// 滚动事件
const checkScrollBottom = () => {
  if (scrollWrapper.value) {
    const { scrollTop, scrollHeight, clientHeight } = scrollWrapper.value;
    isScrollDown.value = scrollHeight - scrollTop - clientHeight > 20;
  }
};

// 刷新对话
const handleRegenerate = async (_chatItem: IChatStreamContent) => {
  const lastMessageIndex = chatMessageList.value.length - 1;
  const lastMessage = chatMessageList.value[lastMessageIndex];

  // 确保最后一条消息是助手并且已完成
  if (lastMessage && lastMessage.role === 'assistant' && lastMessage.isFinish) {
    // 找到用户发的最后一条消息
    let userMessageIndex = -1;
    for (let i = lastMessageIndex - 1; i >= 0; i--) {
      if (chatMessageList.value[i].role === 'user') {
        userMessageIndex = i;
        break;
      }
    }

    if (userMessageIndex !== -1) {
      const userMessage = chatMessageList.value[userMessageIndex];
      // 移除从用户消息之后的所有消息
      chatMessageList.value.splice(userMessageIndex + 1);
      // 重新发送用户消息
      await handleInputSend(userMessage.content);
    }
  }
};

// 切换历史侧边栏显示状态
const toggleHistorySidebar = () => {
  showHistorySidebar.value = !showHistorySidebar.value;
};

// 处理会话选择
const handleSelectConversation = async (data: IConversationData) => {
  if (data && data.conversationId) {
    // 设置当前会话ID
    conversationId.value = data.conversationId;

    // 清空当前消息列表
    chatMessageList.value = [];

    // 重置思考模式标志
    isInThinkingMode.value = false;
    resetThinkingState();

    // 重置发送消息标志，允许发送新消息
    canSendMessage.value = true;

    // 如果有历史消息，加载到界面
    if (data.messageList && Array.isArray(data.messageList)) {
      chatMessageList.value = data.messageList.map((msg: IHistoryMessage, index: number) => ({
        role: msg.role as Role,
        content: msg.content,
        key: Date.now() + Math.random() + index,
        isFinish: true,
        reasoningData: {
          content: msg.reasoning_content || '',
          status: 'finished' as const,
        },
      }));
    }

    showHistorySidebar.value = false;
    showSuccessToast(`已切换到历史会话: ${data.title || '未命名会话'}`);

    // 更新URL路由，确保URL显示正确的title
    if (data.title && route.params.title !== data.title) {
      await router.replace({
        name: 'chat-conversation',
        params: { title: data.title },
      });
    }

    // 标记已初始化，防止路由变化时重复初始化
    isInitialized.value = true;

    // 滚动到底部显示最新消息
    await nextTick(() => {
      scrollToEnd();
    });
  } else {
    console.error('❌ [chat.vue] 历史会话数据格式错误:', data);
    showFailToast('加载历史会话失败');
  }
};

// 获取用户信息
const loadUserInfo = async () => {
  try {
    console.log('🔄 [chat.vue] 开始获取用户信息...');
    const userInfo = await getUserInfo();
    console.log('📡 [chat.vue] 用户信息:', userInfo);

    if (userInfo && userInfo.login) {
      currentMisId.value = userInfo.login;
      userStore.userInfo = userInfo;
      console.log('✅ [chat.vue] 用户信息加载成功, misId:', currentMisId.value);
    } else {
      console.warn('⚠️ [chat.vue] 用户信息格式异常');
      // 设置默认值，避免连接失败
      currentMisId.value = 'unknown_user';
    }
  } catch (error) {
    console.error('❌ [chat.vue] 获取用户信息失败:', error);
    // 设置默认值，避免连接失败
    currentMisId.value = 'unknown_user';
  }
};

onBeforeMount(async () => {
  // 先加载用户信息
  await loadUserInfo();
});

// 添加标志位防止重复初始化
const isInitialized = ref(false);

// 处理历史会话恢复的函数
const handleHistoryRestore = async () => {
  const routeTitle = route.params.title as string;

  // 检查是否有待恢复的会话数据（从sessionStorage）
  const pendingRestoreData = sessionStorage.getItem('pendingConversationRestore');
  if (pendingRestoreData && routeTitle && routeTitle !== 'new') {
    try {
      const conversationData = JSON.parse(pendingRestoreData) as unknown;

      if (
        conversationData &&
        typeof conversationData === 'object' &&
        'conversationId' in conversationData
      ) {
        // 清除sessionStorage中的数据
        sessionStorage.removeItem('pendingConversationRestore');

        // 直接调用handleSelectConversation来恢复历史会话
        await handleSelectConversation(conversationData as IConversationData);
        console.log(
          '✅ [chat.vue] 历史会话恢复成功，会话ID:',
          (conversationData as IConversationData).conversationId,
        );
        return true; // 表示已处理历史会话
      }
      throw new Error('会话数据格式错误');
    } catch (error) {
      console.error('❌ [chat.vue] 解析会话数据失败:', error);
      // 清除无效数据
      sessionStorage.removeItem('pendingConversationRestore');
      // 如果解析失败，创建新会话
      await initNewChat();
      return true; // 表示已处理（虽然失败了）
    }
  }

  // 检查是否需要恢复历史会话（兼容旧的query参数方式）
  const restoreHistory = route.query.restoreHistory as string;
  if (restoreHistory === 'true' && routeTitle && routeTitle !== 'new') {
    // 从query参数中获取会话数据
    const conversationDataStr = route.query.conversationData as string;

    if (conversationDataStr) {
      try {
        const conversationData = JSON.parse(conversationDataStr) as unknown;

        if (
          conversationData &&
          typeof conversationData === 'object' &&
          'conversationId' in conversationData
        ) {
          // 直接调用handleSelectConversation来恢复历史会话
          await handleSelectConversation(conversationData as IConversationData);
          console.log(
            '✅ [chat.vue] 历史会话恢复成功（query方式），会话ID:',
            (conversationData as IConversationData).conversationId,
          );
          return true; // 表示已处理历史会话
        }
        throw new Error('会话数据格式错误');
      } catch (error) {
        console.error('❌ [chat.vue] 解析会话数据失败:', error);
        // 如果解析失败，创建新会话
        await initNewChat();
        return true; // 表示已处理（虽然失败了）
      }
    } else {
      // 如果没有会话数据，创建新会话
      await initNewChat();
      return true; // 表示已处理
    }
  }

  return false; // 表示没有处理历史会话
};

// 监听路由变化
watch(
  () => route.params.title,
  async (newTitle, oldTitle) => {
    // 只有当title参数真正变化时才处理，并且还没有初始化过
    if (newTitle !== oldTitle && !isInitialized.value) {
      console.log('🔄 [chat.vue] 路由参数变化，尝试恢复历史会话:', {
        newTitle,
        oldTitle,
      });
      const historyHandled = await handleHistoryRestore();

      if (!historyHandled) {
        // 初始化新会话（在用户信息加载完成后）
        console.log('由于没有待恢复的会话数据，初始化新会话');
        await initNewChat();
      }

      // 标记已初始化
      isInitialized.value = true;
    }
  },
  { immediate: true },
);

// 虚拟键盘处理相关
const setupKeyboardFix = () => {
  // 判断设备类型
  const ua = window.navigator.userAgent.toLowerCase();
  const isIOS = /iphone|ipad|ipod/.test(ua);

  if (!isIOS) return undefined;

  // 监听页面点击事件，处理点击空白区域收起键盘的情况
  const handlePageClick = (event: Event) => {
    const target = event.target as HTMLElement;

    // 检查点击的是否为输入相关元素
    const isInputElement =
      target.tagName === 'INPUT' ||
      target.tagName === 'TEXTAREA' ||
      target.contentEditable === 'true' ||
      target.closest('.input-bar') ||
      target.closest('.van-field');

    if (!isInputElement) {
      // 点击的是空白区域，延迟执行键盘修复
      setTimeout(() => {
        // 检查微信版本和iOS版本
        const wechatInfo = window.navigator.userAgent.match(/MicroMessenger\/([\d.]+)/i);
        if (!wechatInfo) return;

        const wechatVersion = wechatInfo[1];
        const version = navigator.userAgent.match(/OS (\d+)_(\d+)_?(\d+)?/);

        if (!version) return;

        // 微信版本6.7.4+且iOS12+会出现键盘收起后视图被顶上去的问题
        if (+wechatVersion.replace(/\./g, '') >= 674 && +version[1] >= 12) {
          console.log('🔧 [chat.vue] 执行iOS键盘修复');
          window.scrollTo(
            0,
            Math.max(document.body.clientHeight, document.documentElement.clientHeight),
          );
        }
      }, 300);
    }
  };

  // 添加页面点击监听
  document.addEventListener('click', handlePageClick, true);

  // 返回清理函数
  return () => {
    document.removeEventListener('click', handlePageClick, true);
  };
};

onMounted(async () => {
  // 加载用户信息
  await loadUserInfo();

  // 在获取用户信息后初始化主题（设置用户ID以支持后端同步）
  themeStore.setUserId(currentMisId.value);
  await themeStore.initTheme();

  // 如果还没有通过路由监听初始化，则在这里初始化
  if (!isInitialized.value) {
    console.log('🔄 [chat.vue] onMounted中进行初始化');
    // 尝试处理历史会话恢复
    const historyHandled = await handleHistoryRestore();

    if (!historyHandled) {
      // 初始化新会话（在用户信息加载完成后）
      console.log('由于没有待恢复的会话数据，初始化新会话');
      await initNewChat();
    }

    // 标记已初始化
    isInitialized.value = true;
  }

  // 检查是否需要显示悬浮卡片
  const cardType = route.query.cardType as string;
  if (cardType === 'weather' || cardType === 'record') {
    console.log('🔄 [chat.vue] 检测到卡片类型:', cardType);
    floatingCardType.value = cardType as 'weather' | 'record';
    showFloatingCard.value = true;
  }

  // 处理从首页传递过来的初始消息
  const initialMessage = route.query.initialMessage as string;
  if (initialMessage && initialMessage.trim() && !hasInitialMessageSent.value) {
    console.log('收到初始消息:', initialMessage);
    // 标记已发送初始消息
    hasInitialMessageSent.value = true;
    // 延迟发送初始消息，确保组件完全初始化
    setTimeout(() => {
      void handleInputSend(initialMessage);
    }, 500);
  }

  // 设置虚拟键盘修复
  const cleanupKeyboardFix = setupKeyboardFix();

  // 在组件卸载时清理
  onUnmounted(() => {
    if (cleanupKeyboardFix) {
      cleanupKeyboardFix();
    }
  });
});

onUnmounted(() => {
  // 组件销毁时的清理工作
  console.log('聊天组件已卸载');
  // 重置初始化标志
  isInitialized.value = false;
});
</script>

<style lang="scss" scoped>
.v-chat-container {
  // 背景现在由主题系统控制
  height: 100vh; // 使用视口高度
  overflow: hidden;
  display: flex; // 添加flex布局
  flex-direction: column; // 纵向排列
  position: relative; // 为星空粒子和弹幕组件提供定位基准

  // 虚拟键盘兼容性处理
  @supports (height: 100dvh) {
    height: 100dvh; // 使用动态视口高度，更好地处理虚拟键盘
  }

  // 额外的虚拟键盘处理
  @supports (height: 100svh) {
    height: 100svh; // 使用小视口高度，在某些浏览器中更稳定
  }

  .v-chat {
    height: 100vh;
    display: flex;
    flex-direction: column;
    overflow: hidden; // 防止内容溢出
    position: relative; // 相对定位
    z-index: 10; // 确保内容在星空粒子之上

    // 虚拟键盘兼容性处理
    @supports (height: 100dvh) {
      height: 100dvh; // 使用动态视口高度
    }

    // 额外的虚拟键盘处理
    @supports (height: 100svh) {
      height: 100svh; // 使用小视口高度，在某些浏览器中更稳定
    }

    .header {
      width: 100%;
    }

    .chat-content {
      flex: 1;
      padding: 0px 0px 32px 0px;
      box-sizing: border-box;
      overflow: hidden;
      .chat-scroll-wrapper {
        width: 100%;
        height: 100%;
        padding: 32px 32px 80px 32px; // 增加底部padding，为预设问题和输入框留出充足空间，避免遮挡聊天内容
        overflow-y: auto;
        box-sizing: border-box;
      }
    }

    .footer {
      flex-shrink: 0;
      position: relative;
      z-index: 100;

      .input-wrapper {
        width: 100%;
        padding: 0px;
        background: transparent;
      }
    }
  }
}

// 使用设计系统中的按钮样式
:deep(.regenerate-btn) {
  padding: 18px 28px;
  border-radius: 20px;
  font-size: 18px;
  font-weight: 600;
  cursor: pointer;
  border: 2px solid var(--primary-color);
  background: var(--primary-color-light);
  color: var(--primary-color);
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  max-width: 200px;
  margin: 0 auto;

  &:hover {
    background: var(--primary-color-medium);
    transform: translateY(-2px);
    box-shadow: 0 8px 24px var(--primary-color-strong);
  }
}

// 使用设计系统中的卡片样式
:deep(.message-card) {
  background: var(--bg-glass);
  border: 2px solid var(--border-accent);
  border-radius: 16px;
  padding: 20px;
  backdrop-filter: blur(20px);
  box-shadow: var(--shadow-soft), var(--shadow-accent);
  transition: all 0.3s ease;
  margin-bottom: 24px;
}

// 使用设计系统中的文字样式
:deep(.message-content) {
  color: var(--text-secondary);
  font-size: 16px;
  font-weight: 400;
  line-height: 1.5;
}

:deep(.message-title) {
  color: var(--text-secondary);
  font-size: 20px;
  font-weight: 500;
  margin-bottom: 8px;
}

// 老董假装说话样式 - 作为输入框的下半部分
.laodong-fake-speaking {
  padding: 0px 20px 12px 20px; // 与输入框内部padding一致
  display: flex;
  justify-content: flex-start;
  // 透明背景，与inputBar保持一致
  background: transparent;
  border: 2px solid var(--border-accent); // 添加左右边框
  border-top: none; // 去掉上边框
  border-radius: 0 0 20px 20px; // 只有下方圆角，与输入框形成整体
  margin-top: -2px; // 与输入框无缝连接
  // 移除模糊效果

  .fake-speaking-container {
    display: flex;
    align-items: center; // 改为居中对齐，一行显示
    gap: 12px;
    width: 100%; // 占满宽度

    .laodong-avatar {
      width: 80px; // 与inputBar中user-avatar保持一致
      height: 80px;
      border-radius: 50%;
      overflow: hidden;
      flex-shrink: 0;
      border: 2px solid var(--border-accent);
      box-shadow: var(--shadow-medium);

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }

    .fake-speaking-content {
      display: flex;
      align-items: center;
      gap: 8px;
      flex: 1;

      .fake-speaking-text {
        color: var(--primary-color); // 改为主题色
        font-size: 28px; // 继续增大字体
        font-weight: 500;
        line-height: 1.2;
        white-space: nowrap; // 不换行
      }

      .fake-speaking-dots {
        display: flex;
        gap: 4px;
        align-items: center;

        .dot {
          width: 6px;
          height: 6px;
          border-radius: 50%;
          background: var(--accent-color);
          animation: fakeSpeakingPulse 1.5s ease-in-out infinite;

          &:nth-child(1) {
            animation-delay: 0s;
          }

          &:nth-child(2) {
            animation-delay: 0.3s;
          }

          &:nth-child(3) {
            animation-delay: 0.6s;
          }
        }
      }
    }
  }
}

// 假装说话动画
@keyframes fakeSpeakingPulse {
  0%, 60%, 100% {
    opacity: 0.3;
    transform: scale(1);
  }
  30% {
    opacity: 1;
    transform: scale(1.2);
  }
}

// 响应式适配
@media (max-width: 768px) {
  .v-chat-container {
    .v-chat {
      .chat-content {
        .chat-scroll-wrapper {
          padding: 16px 16px 16px 16px;
        }
      }
    }
  }

  .laodong-fake-speaking {
    padding: 10px 16px; // 移动端保持背景padding

    .fake-speaking-container {
      gap: 10px;

      .laodong-avatar {
        width: 64px; // 与inputBar移动端user-avatar保持一致
        height: 64px;
      }

      .fake-speaking-content {
        gap: 6px;

        .fake-speaking-text {
          font-size: 26px; // 移动端也继续增大字体
        }

        .fake-speaking-dots {
          gap: 3px;

          .dot {
            width: 5px;
            height: 5px;
          }
        }
      }
    }
  }
}
</style>
