<template>
  <div v-if="show" class="dialog-overlay">
    <div class="dialog-container add-dialog">
      <div class="dialog-header">
        <div class="dialog-title">随手记</div>
        <div class="dialog-close" @click="handleClose">
          <DeleteIcon :size="24" color="var(--primary-color)" />
        </div>
      </div>

      <div class="dialog-content add-content">
        <!-- 自然语言添加区域 -->
        <div class="natural-add-section">
          <!-- 输入提示 -->
          <div class="input-hint">
            <div class="hint-desc">让老董更懂你，分享你的日常生活：</div>
            <div class="hint-examples">
              <div class="example-item">• 我周三要和张山一起去吃北京烤鸭</div>
              <div class="example-item">• 昨天和小李在公司开了项目启动会议</div>
              <div class="example-item">• 下周五和家人去看周深的演唱会</div>
            </div>
          </div>

          <!-- 文字输入框 -->
          <div class="input-group">
            <label class="input-label">事件内容</label>
            <div class="input-content-wrapper">
              <textarea
                v-model="inputText"
                class="input-field natural-input"
                :placeholder="isRecording ? '我在听，请说...' : '请用自然语言描述您的事件...'"
                maxlength="200"
                rows="3"
              ></textarea>
              <!-- 麦克风按钮在输入框内部右侧 -->
              <div class="voice-toggle-inner" :class="{ breathing: isRecording }" @click="handleVoiceButtonClick">
                <MicrophoneIcon :size="16" />
              </div>
            </div>
            <div class="char-count">{{ inputText.length }}/200</div>
          </div>

          <!-- 提交按钮 -->
          <div class="submit-area">
            <button
              class="send-btn"
              :class="{ 'not-input': !inputText.trim() || isSubmitting }"
              :disabled="!inputText.trim() || isSubmitting"
              @click="handleSubmit"
            >
              <span v-if="isSubmitting">创建中...</span>
              <span v-else>创建事件</span>
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, onBeforeUnmount } from 'vue';
import { addPersonEventNatural, type IAddEventNaturalRequest } from '@/apis/memory';
import { showSuccessToast, showToast } from 'vant';
import { getStreamAsr } from '@/apis/chat';
import { generateRandomString } from '@/utils';
import { debounce } from 'lodash-es';
import Recorder from 'recorder-realtime';
import DeleteIcon from '@/assets/icons/DeleteIcon.vue';
import MicrophoneIcon from '@/assets/icons/MicrophoneIcon.vue';

// Props定义
interface IProps {
  show: boolean;
  userId: string;
  personId: string;
}

const props = defineProps<IProps>();

// Emits定义
const emit = defineEmits<{
  close: [];
}>();

// 响应式数据
const inputText = ref('');
const isSubmitting = ref(false);

// 语音相关状态
const isRecording = ref(false);
const recognizedText = ref('');
const micPermission = ref(false);
const audioBufferIndex = ref(0);
const sessionId = ref('');
const lastBuffer = ref<ArrayBuffer | null>(null);

// 录音器和定时器
// eslint-disable-next-line @typescript-eslint/no-explicit-any
let recorder: any = null;
let timerId: ReturnType<typeof setTimeout> | null = null;
// 保存媒体流引用，用于释放麦克风资源
let mediaStream: MediaStream | null = null;

// 重置状态
const resetState = () => {
  inputText.value = '';
  isSubmitting.value = false;
  isRecording.value = false;
  recognizedText.value = '';
  audioBufferIndex.value = 0;
  sessionId.value = '';
  lastBuffer.value = null;

  if (timerId !== null) {
    clearTimeout(timerId);
    timerId = null;
  }

  releaseMicrophoneResources();
};

// 释放麦克风资源
const releaseMicrophoneResources = () => {
  if (mediaStream) {
    // 停止所有媒体轨道，释放麦克风资源
    mediaStream.getTracks().forEach((track) => {
      track.stop();
      console.log('🎤 [AddEventDialog] 释放麦克风轨道:', track.kind);
    });
    mediaStream = null;
    micPermission.value = false;
    console.log('✅ [AddEventDialog] 麦克风资源已释放');
  }
};

// 设置麦克风权限
const setMicPermission = async () => {
  try {
    // 保存媒体流引用，用于后续释放资源
    mediaStream = await navigator.mediaDevices.getUserMedia({ audio: true });
    micPermission.value = true;
    if (!recorder) {
      initRecorder();
    }
  } catch (error) {
    console.error('获取麦克风权限失败:', error);
    micPermission.value = false;
    showToast('请允许麦克风权限以使用语音功能');
  }
};

// 初始化录音器
const initRecorder = () => {
  if (!Recorder.isRecordingSupported()) {
    // 如果浏览器不支持录音功能，给用户提示
    showToast('录音失败，浏览器不支持录音功能');
    return;
  }

  recorder = new Recorder({
    recordingGain: 1,
    numberOfChannels: 1,
    wavBitDepth: 16,
    format: 'pcm',
    wavSampleRate: 16000,
    streamPages: true,
    bufferLength: 4096,
  });

  // 当录音开始时的回调
  recorder.onstart = () => {};

  // 处理录音错误的回调
  recorder.onstreamerror = () => {
    // 显示录音错误消息并停止录音
    showToast('录音失败');
    stopVoiceRecording().catch(console.error);
  };

  // 当数据可用时的回调
  recorder.ondataavailable = async (data: { command: string; buffer: ArrayBuffer }) => {
    if (data.command === 'buffer') {
      lastBuffer.value = data.buffer;
      audioBufferIndex.value += 1;
      try {
        const streamData = await getStreamAsr({
          sessionId: sessionId.value,
          format: 'pcm',
          sampleRate: 16000,
          index: audioBufferIndex.value,
          data: data.buffer,
        });
        // 修复：检查 full_text 而不是 text，并且确保 full_text 不为空且与当前值不同
        if (
          streamData.data.full_text &&
          streamData.data.full_text.trim() !== '' &&
          streamData.data.full_text !== recognizedText.value
        ) {
          recognizedText.value = streamData.data.full_text;
          // 将识别到的文字实时输入到输入框中
          inputText.value = streamData.data.full_text;
          await autoSendTimeout();
        }
      } catch (error) {
        console.error('❌ [AddEventDialog] 语音识别失败:', error);
        showToast('语音识别失败，请重试');
      }
    }
  };
};

// 两秒不说话自动发送
const autoSendTimeout = debounce(async () => {
  await stopVoiceRecording();
}, 2000);

// 处理语音按钮点击 - 参考inputBar逻辑
const handleVoiceButtonClick = async () => {
  if (isRecording.value) {
    await stopVoiceRecording();
  } else {
    await startVoiceRecording();
  }
};

// 开始语音录音
const startVoiceRecording = async () => {
  if (isRecording.value) {
    // 如果正在录音，停止录音
    await stopVoiceRecording();
    return;
  }

  // 如果没有麦克风权限，先请求权限
  if (!micPermission.value) {
    await setMicPermission();
  }

  if (micPermission.value) {
    isRecording.value = true;
    recognizedText.value = '';
    audioBufferIndex.value = 0;
    sessionId.value = generateRandomString(32);
    if (!recorder) {
      initRecorder();
    }
    recorder.start();

    timerId = setTimeout(async () => {
      showToast('录音已达到最大时长');
      await stopVoiceRecording();
    }, 1000 * 60);
  }
};

// 停止语音录音
const stopVoiceRecording = async () => {
  if (!isRecording.value) return;
  isRecording.value = false;
  if (timerId !== null) {
    clearTimeout(timerId);
    timerId = null;
  }
  if (recorder) {
    recorder.stop();
  }

  // 释放麦克风资源
  releaseMicrophoneResources();

  try {
    await getStreamAsr({
      sessionId: sessionId.value,
      format: 'pcm',
      sampleRate: 16000,
      index: audioBufferIndex.value * -1,
      data: null,
    });
  } catch (error) {
    console.error('❌ [AddEventDialog] 结束语音识别失败:', error);
  }

  if (recognizedText.value) {
    // 将识别到的文字保留在输入框中，不自动发送
    inputText.value = recognizedText.value;
    console.log('📤 [AddEventDialog] 语音识别完成:', recognizedText.value);
  } else {
    showToast('录音解析为空，请重新录制~');
  }
  // 清空语音消息
  recognizedText.value = '';
};

// 处理提交
const handleSubmit = () => {
  if (!inputText.value.trim() || isSubmitting.value) {
    return;
  }

  const content = inputText.value.trim();
  handleSubmitEvent(content);
};

// 提交事件
const handleSubmitEvent = (eventText: string) => {
  if (!eventText.trim() || isSubmitting.value) return;

  console.log('🚀 [AddEventDialog] 开始提交自然语言事件:', eventText, 'personId:', props.personId);

  // 立即关闭弹窗，给用户快速反馈
  resetState();
  emit('close');

  isSubmitting.value = true;

  // 在后台继续API调用，不阻塞UI
  // 使用setTimeout确保API调用不会因为组件销毁而中断
  setTimeout(async () => {
    try {
      // 构建请求参数
      const requestData: IAddEventNaturalRequest = {
        user_id: props.userId,
        person_id: props.personId,
        event_text: eventText,
      };

      console.log('📤 [AddEventDialog] 自然语言事件请求参数:', requestData);

      // 调用自然语言添加事件API
      const response = await addPersonEventNatural(requestData);

      console.log('📡 [AddEventDialog] 自然语言事件响应:', response);

      if (response && response.result === 'success') {
        console.log('✅ [AddEventDialog] 事件添加成功');
        showSuccessToast('事件添加成功！');

        // 延迟1000ms后发送自定义事件通知刷新MemorySection
        setTimeout(() => {
          window.dispatchEvent(
            new CustomEvent('addeventsuccess', {
              detail: {
                userId: props.userId,
                personId: props.personId,
                eventId: response.event_id,
              },
            }),
          );
        }, 1000);
      } else {
        console.warn('⚠️ [AddEventDialog] 添加事件失败:', response);
        showToast('事件添加失败，请重试');
      }
    } catch (error) {
      console.error('❌ [AddEventDialog] 提交事件失败:', error);
      showToast('事件添加失败，请重试');
    } finally {
      isSubmitting.value = false;
    }
  }, 100); // 延迟100ms执行，确保弹窗关闭流程不受影响
};

// 监听show变化，重置状态
watch(
  () => props.show,
  (newValue) => {
    if (newValue) {
      resetState();
    }
  },
);

// 处理关闭
const handleClose = () => {
  if (isSubmitting.value) return;

  resetState();
  emit('close');
};

// 组件卸载时清理
onBeforeUnmount(() => {
  resetState();
});
</script>

<style scoped lang="scss">
.dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--overlay-dark);
  backdrop-filter: blur(20px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
  animation: fadeIn 0.3s ease-out;
}

.dialog-container {
  background: var(--bg-glass-popup);
  border: none;
  border-radius: 16px;
  padding: 30px;
  box-sizing: border-box;
  backdrop-filter: blur(10px);
  border-left: 4px solid var(--accent-color);
  box-shadow: var(--shadow-accent);
  transition: all 0.3s ease;
  width: 90%;
  max-width: 600px;
  color: var(--text-primary);
  animation: slideInScale 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
  display: flex;
  flex-direction: column;

  &.add-dialog {
    max-height: 90vh;
    overflow-y: auto;
  }
}

.dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 13px;
  margin-bottom: 18px;
  padding-bottom: 18px;
  position: relative;

  .dialog-title {
    color: var(--primary-color);
    font-size: 36px; // 增加4px (原来32px)
    font-weight: 600;
    flex-shrink: 0;
  }

  .dialog-close {
    border-radius: 50%;
    width: 48px;
    height: 48px;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    transition: all 0.3s ease;
  }

  .close-icon {
    width: 24px;
    height: 24px;
    filter: brightness(0) saturate(100%) invert(27%) sepia(51%) saturate(2878%) hue-rotate(346deg) brightness(104%)
      contrast(97%);
  }
}

.dialog-content {
  flex: 1;
  overflow-y: auto;

  &.add-content {
    padding-top: 0;
  }
}

// 自然语言添加区域
.natural-add-section {
  .input-hint {
    background: var(--primary-color-light);
    backdrop-filter: blur(10px);
    border-left: 4px solid var(--accent-color);
    box-shadow: var(--shadow-accent);
    border-radius: 16px;
    padding: 24px;
    margin-bottom: 32px;
    max-height: 600px;
    overflow-y: auto;

    .hint-title {
      color: var(--primary-color);
      font-size: 32px; // 增加4px (原来28px)
      font-weight: 600;
      margin-bottom: 12px;
    }

    .hint-desc {
      color: var(--person-detail-context);
      font-size: 28px; // 增加4px (原来24px)
      margin-bottom: 16px;
    }

    .hint-examples {
      display: flex;
      flex-direction: column;
      gap: 8px;

      .example-item {
        color: var(--person-detail-context);
        font-size: 28px; // 增加4px (原来22px)
      }
    }
  }

  // 语音识别文字显示
  .voice-text-display {
    background: var(--primary-color-light);
    border: 2px solid var(--primary-color);
    border-radius: 12px;
    padding: 24px;
    margin-bottom: 24px;
    min-height: 80px;
    display: flex;
    align-items: center;
    justify-content: center;

    .voice-placeholder {
      color: var(--person-detail-context);
      font-size: 28px; // 增加4px (原来24px)
      font-style: italic;
    }

    .voice-message-text {
      color: var(--person-detail-context);
      font-size: 28px; // 增加4px (原来24px)
      line-height: 1.5;
      text-align: center;
    }
  }

  // 输入组
  .input-group {
    margin-bottom: 32px;
    position: relative;

    .input-label {
      display: block;
      color: var(--primary-color);
      font-size: 32px; // 增加4px (原来28px)
      font-weight: 500;
      margin-bottom: 12px;
    }

    .input-content-wrapper {
      width: 100%;
      position: relative;
      display: flex;
      align-items: stretch;

      .voice-toggle-inner {
        position: absolute;
        right: 16px;
        top: 24px;
        width: 60px;
        height: 60px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        border-radius: 50%;
        background: var(--bg-glass);
        border: 2px solid var(--primary-color);
        backdrop-filter: blur(20px);
        transition: all 0.3s ease;
        z-index: 10;

        &.breathing {
          animation: breathing 2s ease-in-out infinite;
        }

        .iconfont {
          font-size: 28px;
          color: var(--primary-color);
        }
      }
    }

    .input-field {
      width: 100%;
      height: 300px;
      background: var(--bg-glass);
      border: 2px solid var(--bg-glass-hover);
      border-radius: 12px;
      padding: 16px 90px 16px 16px; // 右侧留出空间给麦克风按钮
      color: var(--person-detail-context);
      font-size: 28px; // 增加4px (原来26px)
      outline: none;
      transition: all 0.3s ease;
      box-sizing: border-box;

      &::placeholder {
        color: var(--person-detail-context);
      }

      &:focus {
        border-color: var(--primary-color);
        box-shadow: 0 0 0 4px var(--primary-color-medium);
      }

      &.natural-input {
        resize: vertical;
        min-height: 120px;
        line-height: 1.5;
      }
    }

    .char-count {
      position: absolute;
      bottom: 12px;
      right: 16px;
      color: var(--person-detail-context);
      font-size: 24px; // 增加4px (原来20px)
      pointer-events: none;
    }
  }

  // 提交按钮区域
  .submit-area {
    margin-top: 32px;
    display: flex;
    justify-content: center;

    .send-btn {
      flex: 1;
      padding: 16px 16px;
      border-radius: 20px;
      font-size: 36px;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.3s ease;
      border: 2px solid var(--primary-color);
      background: var(--bg-glass);
      backdrop-filter: blur(10px);
      color: var(--primary-color);
      height: 60px;
      display: flex;
      align-items: center;
      justify-content: center;
      min-width: 200px;

      &:hover:not(.not-input) {
        background: var(--primary-color-medium);
        transform: translateY(-2px);
        box-shadow: var(--shadow-accent);
      }

      &.not-input {
        opacity: 0.6;
        cursor: not-allowed;
      }
    }
  }
}

// 动画定义
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideInScale {
  from {
    opacity: 0;
    transform: scale(0.8) translateY(20px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

@keyframes breathing {
  0% {
    transform: scale(1);
    box-shadow: 0 0 0 0 var(--primary-color-strong);
  }
  50% {
    transform: scale(1.1);
    box-shadow: 0 0 0 8px var(--primary-color-medium);
  }
  100% {
    transform: scale(1);
    box-shadow: 0 0 0 0 var(--primary-color-strong);
  }
}
</style>
