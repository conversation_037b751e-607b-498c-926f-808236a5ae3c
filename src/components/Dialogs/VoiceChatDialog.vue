<template>
  <div class="dialog-overlay">
    <div class="dialog-container voice-chat-dialog">
      <div class="dialog-header">
        <div class="dialog-title">对话修改</div>
        <div class="dialog-close" @click="handleClose">
          <img src="@/assets/icon/close.png" alt="关闭" class="close-icon" />
        </div>
      </div>

      <div class="dialog-content voice-chat-content">
        <!-- 上部信息展示区域 -->
        <div class="info-display-section">
          <div class="section-header">
            <span class="section-icon">{{ sectionInfo.icon }}</span>
            <span class="section-title">{{ sectionInfo.title }}</span>
          </div>
          <div class="section-content">
            <div class="content-text">{{ sectionInfo.content }}</div>
          </div>
        </div>

        <!-- 下部语音对话区域 -->
        <div class="voice-chat-section">
          <!-- 聊天消息列表 -->
          <div ref="chatMessagesRef" class="chat-messages">
            <div
              v-for="message in chatMessages"
              :key="message.key"
              class="chat-message"
              :class="message.role"
            >
              <div
                class="message-content"
                :class="{
                  'loading-content': !message.isFinish && message.role === 'assistant',
                }"
              >
                <!-- 显示消息内容 -->
                <div v-if="message.content || message.isFinish">
                  {{ message.content }}
                </div>
                <!-- 显示loading动画 -->
                <div v-else-if="!message.isFinish && message.role === 'assistant'" class="loading">
                  <div class="dot"></div>
                  <div class="dot"></div>
                  <div class="dot"></div>
                </div>
              </div>
            </div>
          </div>

          <!-- 语音输入区域 -->
          <div class="voice-input-area">
            <!-- 识别文字显示区域 -->
            <div v-if="recognizedText" class="recognized-text">
              {{ recognizedText }}
            </div>

            <!-- 语音输入模式 -->
            <div v-if="!isKeyboardMode" class="voice-input-mode">
              <div class="voice-prompt-text">解答跟ta有关的问题</div>
              <button
                class="voice-btn"
                :class="{ recording: isRecording }"
                @click="toggleVoiceRecording"
              >
                <div class="voice-button-bg" :class="{ recording: isRecording }"></div>
                <img src="@/assets/icon/mic.png" alt="语音" class="voice-mic-icon" />
              </button>

              <button class="keyboard-toggle-btn" @click="toggleInputMode">
                <span class="chevron-right">></span>
              </button>
            </div>

            <!-- 键盘输入模式 -->
            <div v-if="isKeyboardMode" class="keyboard-input-mode">
              <button class="voice-toggle-btn-circle" @click="toggleInputMode">
                <div class="voice-button-bg"></div>
                <img src="@/assets/icon/mic.png" alt="切换语音" class="voice-mic-icon" />
              </button>

              <div class="text-input-container">
                <input
                  ref="textInputRef"
                  v-model="textInput"
                  type="text"
                  placeholder="解答和ta有关的内容"
                  class="text-input"
                  @keydown.enter="handleTextSend"
                />
              </div>

              <div
                class="send-btn"
                :class="{
                  'not-input':
                    !textInput.trim() || chatStore.answerStatus === AnswerStatusEnum.LOADING,
                }"
                @click="handleTextSend"
              >
                <SendIcon class="send-icon" :size="46" />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount, nextTick } from 'vue';
import { streamChat, createConversation, type IToolCall } from '@/apis/chat';
import { Typewriter } from '@/utils/typeWriter';
import { useChatStore } from '@/stores/chat';
import { AnswerStatusEnum } from '@/constants/chat';
import { getStreamAsr } from '@/apis/chat';
import { generateRandomString } from '@/utils';
import { showToast } from 'vant';
import { debounce } from 'lodash-es';
import Recorder from 'recorder-realtime';
import SendIcon from '@/assets/icons/SendIcon.vue';

// 聊天消息类型定义
interface IChatStreamContent {
  role: 'user' | 'assistant';
  content: string;
  key: number | string;
  isFinish: boolean;
}

// Props定义
interface IProps {
  sectionInfo: {
    title: string;
    icon: string;
    content: string;
  };
  personName: string;
  userId: string;
}

const props = defineProps<IProps>();

// Emits定义
const emit = defineEmits<{
  close: [];
  chatComplete: []; // 新增：对话完成事件，用于通知父组件刷新数据
}>();

// 聊天相关状态
const chatStore = useChatStore();
const chatMessages = ref<IChatStreamContent[]>([]);
const chatMessagesRef = ref<HTMLElement>();
const conversationId = ref('');
const streamController = ref<AbortController | null>(null);

// 打字机相关状态
const typewriter = new Typewriter(async (str: string) => {
  if (str && chatMessages.value.length > 0) {
    const lastMessage = chatMessages.value[chatMessages.value.length - 1];
    if (lastMessage.role === 'assistant') {
      lastMessage.content = str;
      await nextTick(() => {
        scrollChatToBottom();
      });
    }
  }
});
const isTypewriterStarted = ref(false);

// mention选择器相关
const mentionSelector = ref<HTMLElement>();
const mentionWidth = ref(120);

// 语音录音相关状态
const isRecording = ref(false);
const recognizedText = ref('');
const micPermission = ref(false);
const sessionId = ref('');
const audioBufferIndex = ref(0);
const lastBuffer = ref<ArrayBuffer | null>(null);

// 文字输入相关状态
const textInput = ref('');
const textInputRef = ref(); // 文字输入框引用
// 输入模式状态：false为语音输入模式，true为键盘输入模式
const isKeyboardMode = ref(false);

// eslint-disable-next-line @typescript-eslint/no-explicit-any
let recorder: any = null;
let timerId: ReturnType<typeof setTimeout> | null = null;
let mediaStream: MediaStream | null = null;

// 处理关闭
const handleClose = () => {
  // 清理正在进行的请求
  if (streamController.value) {
    streamController.value.abort();
    streamController.value = null;
  }

  // 重置聊天状态
  resetChatState();

  emit('close');
};

// 重置聊天状态
const resetChatState = () => {
  if (streamController.value) {
    streamController.value.abort();
    streamController.value = null;
  }

  chatStore.setAnswerStatus(AnswerStatusEnum.SUCCESS);
  isTypewriterStarted.value = false;
  typewriter.done();
};

// 初始化聊天会话
const initChatConversation = async () => {
  // 确保聊天状态是干净的
  resetChatState();

  if (!conversationId.value) {
    try {
      const response = await createConversation({
        user_id: props.userId,
      });
      if (response && response.conversation_id) {
        conversationId.value = response.conversation_id;
        console.log('✅ [VoiceChatDialog] 聊天会话初始化成功:', conversationId.value);
      }
    } catch (error) {
      console.error('❌ [VoiceChatDialog] 初始化聊天会话失败:', error);
    }
  }
};

// 滚动聊天到底部
const scrollChatToBottom = () => {
  if (chatMessagesRef.value) {
    chatMessagesRef.value.scrollTop = chatMessagesRef.value.scrollHeight;
  }
};

// 更新mention选择器宽度
const updateMentionWidth = async () => {
  await nextTick();
  if (mentionSelector.value) {
    const rect = mentionSelector.value.getBoundingClientRect();
    mentionWidth.value = rect.width + 16; // 添加一些padding
  }
};

// 切换输入模式
const toggleInputMode = () => {
  isKeyboardMode.value = !isKeyboardMode.value;
  // 清空识别文字
  recognizedText.value = '';
};

// 释放麦克风资源
const releaseMicrophoneResources = () => {
  if (mediaStream) {
    mediaStream.getTracks().forEach((track) => track.stop());
    mediaStream = null;
  }
};

// 设置麦克风权限
const setMicPermission = async () => {
  try {
    mediaStream = await navigator.mediaDevices.getUserMedia({ audio: true });
    micPermission.value = true;
    if (!recorder) {
      initRecorder();
    }
  } catch (error) {
    micPermission.value = false;
    showToast('请授权麦克风权限~');
  }
};

// 初始化录音机
const initRecorder = () => {
  if (!Recorder.isRecordingSupported()) {
    showToast('录音失败，浏览器不支持录音功能');
    return;
  }

  recorder = new Recorder({
    recordingGain: 1,
    numberOfChannels: 1,
    wavBitDepth: 16,
    format: 'pcm',
    wavSampleRate: 16000,
    streamPages: true,
    bufferLength: 4096,
  });

  recorder.onstart = () => {};

  recorder.onstreamerror = () => {
    showToast('录音失败');
    cancelRecording();
  };

  recorder.ondataavailable = async (data: { command: string; buffer: ArrayBuffer }) => {
    if (data.command === 'buffer') {
      lastBuffer.value = data.buffer;
      audioBufferIndex.value += 1;
      const streamData = await getStreamAsr({
        sessionId: sessionId.value,
        format: 'pcm',
        sampleRate: 16000,
        index: audioBufferIndex.value,
        data: data.buffer,
      });
      // 修复：检查 full_text 而不是 text，并且确保 full_text 不为空且与当前值不同
      if (
        streamData.data.full_text &&
        streamData.data.full_text.trim() !== '' &&
        streamData.data.full_text !== recognizedText.value
      ) {
        recognizedText.value = streamData.data.full_text;
        await autoSendTimeout();
      }
    }
  };
};

// 两秒不说话自动发送
const autoSendTimeout = debounce(async () => {
  await stopVoiceRecording();
}, 2000);

// 取消录音
const cancelRecording = () => {
  if (!isRecording.value) return;
  isRecording.value = false;
  if (timerId !== null) {
    clearTimeout(timerId);
    timerId = null;
  }
  if (recorder) {
    recorder.stop();
  }
  releaseMicrophoneResources();
  recognizedText.value = '';
};

// 切换语音录音状态
const toggleVoiceRecording = async () => {
  if (isRecording.value) {
    // 如果正在录音，停止录音
    await stopVoiceRecording();
  } else {
    // 如果没有录音，开始录音
    await startVoiceRecording();
  }
};

// 开始语音录音
const startVoiceRecording = async () => {
  // 如果没有麦克风权限，先请求权限
  if (!micPermission.value) {
    await setMicPermission();
  }

  if (micPermission.value) {
    isRecording.value = true;
    recognizedText.value = '';
    audioBufferIndex.value = 0;
    sessionId.value = generateRandomString(32);
    if (!recorder) {
      initRecorder();
    }
    recorder.start();

    timerId = setTimeout(async () => {
      showToast('录音已达到最大时长');
      await stopVoiceRecording();
    }, 1000 * 60);
  }
};

// 停止语音录音
const stopVoiceRecording = async () => {
  if (!isRecording.value) return;
  isRecording.value = false;
  if (timerId !== null) {
    clearTimeout(timerId);
    timerId = null;
  }
  if (recorder) {
    recorder.stop();
  }

  releaseMicrophoneResources();

  await getStreamAsr({
    sessionId: sessionId.value,
    format: 'pcm',
    sampleRate: 16000,
    index: audioBufferIndex.value * -1,
    data: null,
  });

  if (recognizedText.value) {
    // 直接发送语音识别的文字
    await handleVoiceSend(recognizedText.value);
  } else {
    showToast('录音解析为空，请重新录制~');
  }
};

// 处理文字发送
const handleTextSend = async () => {
  // 如果输入框为空或正在加载中，直接返回
  if (!textInput.value.trim() || chatStore.answerStatus === AnswerStatusEnum.LOADING) return;

  const content = textInput.value.trim();
  textInput.value = ''; // 清空输入框

  await handleSendMessage(content);
};

// 处理语音发送
const handleVoiceSend = async (content: string) => {
  await handleSendMessage(content);
};

// 通用发送消息方法
const handleSendMessage = async (content: string) => {
  if (!content.trim() || chatStore.answerStatus === AnswerStatusEnum.LOADING) return;

  console.log('🚀 [VoiceChatDialog] 开始发送语音消息:', content);

  // 如果有正在进行的请求，先取消它
  if (streamController.value) {
    console.log('🔄 [VoiceChatDialog] 取消正在进行的请求');
    streamController.value.abort();
    streamController.value = null;
  }

  // 清空识别文字
  recognizedText.value = '';

  // 重置状态
  isTypewriterStarted.value = false;
  typewriter.done(); // 确保打字机完全停止

  // 添加用户消息
  chatMessages.value.push({
    role: 'user',
    content,
    key: Date.now(),
    isFinish: true,
  });

  // 添加助手消息占位符
  const assistantMessage: IChatStreamContent = {
    role: 'assistant',
    content: '',
    key: Date.now() + 1,
    isFinish: false,
  };
  chatMessages.value.push(assistantMessage);

  // 滚动到底部
  await nextTick(() => {
    scrollChatToBottom();
  });

  // 设置加载状态
  chatStore.setAnswerStatus(AnswerStatusEnum.LOADING);

  try {
    // 创建新的AbortController
    streamController.value = new AbortController();

    // 构建请求数据 - 包含section信息和用户问题
    const sectionContext = `当前正在修改${props.personName}的${props.sectionInfo.title}信息。当前内容：${props.sectionInfo.content}`;
    const finalContent = `${sectionContext}\n\n用户问题：${content}`;

    const requestData = {
      content: finalContent,
      conversation_id: conversationId.value,
      user_id: props.userId,
    };

    console.log('📤 [VoiceChatDialog] 发送聊天请求:', requestData);

    // 开始流式聊天
    await streamChat(
      requestData,
      {
        onMessage: (messageContent: string, isFinal: boolean) => {
          // 添加数据片段到打字机队列
          typewriter.add(messageContent);

          // 只在第一个数据包到达时启动打字机
          if (!isTypewriterStarted.value) {
            isTypewriterStarted.value = true;
            typewriter.start();
          }
          if (isFinal) {
            typewriter.done();
            chatStore.setAnswerStatus(AnswerStatusEnum.SUCCESS);
            streamController.value = null;
            console.log('🏁 [VoiceChatDialog] 对话完全结束，清空streamController');
            emit('chatComplete');
          }
        },
        onPreResponse: (PreResponseContent: string, stage: string) => {
          console.log('🔍 [VoiceChatDialog] 收到预响应内容:', PreResponseContent, stage);
        },
        onToolCall: (toolCall: IToolCall) => {
          console.log('🔧 [VoiceChatDialog] 工具调用:', toolCall);
        },
        onRecommendations: (recommendations: string[]) => {
          console.log('💡 [VoiceChatDialog] 收到推荐问题:', recommendations);

          // 收到推荐问题时，标记消息完成
          assistantMessage.isFinish = true;

          // 结束打字机，立即显示剩余内容
          typewriter.done();

          // 重置状态
          chatStore.setAnswerStatus(AnswerStatusEnum.SUCCESS);

          // 收到推荐问题表示整个对话真正结束，现在可以安全清空controller
          streamController.value = null;
          console.log('🏁 [VoiceChatDialog] 对话完全结束，清空streamController');

          // 通知父组件对话完成，需要刷新数据
          emit('chatComplete');
        },
        onEnd: () => {
          console.log('✅ [VoiceChatDialog] 收到end信号');

          // 注意：这里的end信号可能是工具调用结束，不一定是整个回答结束
          // 不应该设置 isFinish = true，因为回答可能还在继续
          // 只有收到 type: "recommendations" 时才是真正的回答结束

          // ⚠️ 不要在这里清空streamController，因为SSE连接可能还在活跃
          // streamController应该在onRecommendations或onError中清空
          console.log('🔄 [VoiceChatDialog] 保持SSE连接活跃，等待后续消息');
        },
        onError: (error: Error) => {
          console.error('❌ [VoiceChatDialog] 流式聊天错误:', error);
          assistantMessage.content = '抱歉，发生了错误，请稍后重试。';
          assistantMessage.isFinish = true;
          chatStore.setAnswerStatus(AnswerStatusEnum.SUCCESS);
        },
      },
      streamController.value.signal,
    );
  } catch (error) {
    console.error('❌ [VoiceChatDialog] 发送消息失败:', error);
    assistantMessage.content = '抱歉，发生了错误，请稍后重试。';
    assistantMessage.isFinish = true;
    chatStore.setAnswerStatus(AnswerStatusEnum.SUCCESS);
  }

  // 滚动到底部
  await nextTick(() => {
    scrollChatToBottom();
  });
};

// 组件挂载时初始化
onMounted(async () => {
  await initChatConversation();
  await updateMentionWidth();
});

// 组件卸载时清理
onBeforeUnmount(() => {
  // 清理聊天状态
  resetChatState();

  // 清理语音录音资源
  if (isRecording.value) {
    if (recorder) {
      recorder.stop();
    }
    isRecording.value = false;
  }
  releaseMicrophoneResources();
});
</script>

<style scoped lang="scss">
// 弹窗遮罩层
.dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--overlay-dark);
  backdrop-filter: blur(20px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
  animation: fadeIn 0.3s ease-out;
}

// 弹窗容器
.dialog-container {
  background: var(--bg-glass-popup);
  border: none;
  border-radius: 16px;
  padding: 30px;
  box-sizing: border-box;
  backdrop-filter: blur(10px);
  border-left: 4px solid var(--accent-color);
  box-shadow: -4px 0 8px var(--accent-color-strong);
  transition: all 0.3s ease;
  width: 600px;
  color: var(--text-primary);
  animation: slideInScale 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
  display: flex;
  flex-direction: column;

  &.voice-chat-dialog {
    max-width: 700px;
    height: 1200px;
    overflow: hidden;
  }
}

// 弹窗头部
.dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid var(--border-glass);

  .dialog-title {
    font-size: 36px; // 增加8px (原来28px)
    font-weight: 600;
    color: var(--text-secondary);
  }

  .dialog-close {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    border: 2px solid var(--border-glass);
    background: transparent;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;

    &:hover {
      background: var(--bg-glass);
      border-color: var(--border-accent);
      transform: translateY(-2px);
      box-shadow: var(--shadow-soft);
    }

    .close-icon {
      width: 20px;
      height: 20px;
      filter: brightness(0) invert(1);
    }
  }
}

// 弹窗内容
.dialog-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 24px;
  overflow: hidden;

  &.voice-chat-content {
    height: 100%;
  }
}

// 信息展示区域
.info-display-section {
  border: none;
  border-radius: 16px;
  padding: 20px;
  background: var(--bg-glass);
  backdrop-filter: blur(10px);
  border-left: 4px solid var(--accent-color);
  box-shadow: -4px 0 8px var(--accent-color-strong);
  max-height: 250px;
  overflow-y: auto;

  .section-header {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 16px;

    .section-icon {
      font-size: 34px; // 增加8px (原来26px)
    }

    .section-title {
      font-size: 34px; // 增加8px (原来26px)
      font-weight: 600;
      color: var(--text-secondary);
    }
  }

  .section-content {
    .content-text {
      color: var(--text-tertiary);
      line-height: 1.6;
      font-size: 30px; // 增加8px (原来22px)
      white-space: pre-wrap;
    }
  }
}

// 语音对话区域
.voice-chat-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  border: none;
  border-radius: 16px;
  padding: 20px;
  background: var(--bg-glass);
  backdrop-filter: blur(10px);
  border-left: 4px solid var(--accent-color);
  box-shadow: -4px 0 8px var(--accent-color-strong);
  overflow: hidden;
}

// 聊天消息列表
.chat-messages {
  flex: 1;
  overflow-y: auto;
  padding: 16px 0;
  display: flex;
  flex-direction: column;
  gap: 16px;

  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: var(--bg-glass);
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background: var(--border-glass);
    border-radius: 3px;

    &:hover {
      background: var(--border-accent);
    }
  }
}

// 聊天消息
.chat-message {
  display: flex;
  margin-bottom: 16px;

  &.user {
    justify-content: flex-end;

    .message-content {
      background: var(--primary-color-medium);
      border: 1px solid var(--primary-color-strong);
      border-radius: 18px 18px 4px 18px;
      padding: 12px 16px;
      max-width: 70%;
      color: var(--text-secondary);
      font-size: 30px; // 增加8px (原来22px)
      line-height: 1.5;
      word-wrap: break-word;
    }
  }

  &.assistant {
    justify-content: flex-start;

    .message-content {
      background: var(--bg-glass);
      border: 1px solid var(--border-glass);
      border-radius: 18px 18px 18px 4px;
      padding: 12px 16px;
      max-width: 70%;
      color: var(--text-tertiary);
      font-size: 30px; // 增加8px (原来22px)
      line-height: 1.5;
      word-wrap: break-word;

      &.loading-content {
        min-height: 40px;
        display: flex;
        align-items: center;
      }
    }
  }
}

// Loading动画
.loading {
  display: flex;
  gap: 4px;
  align-items: center;

  .dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: var(--text-disabled);
    animation: loadingDot 1.4s infinite ease-in-out both;

    &:nth-child(1) {
      animation-delay: -0.32s;
    }

    &:nth-child(2) {
      animation-delay: -0.16s;
    }

    &:nth-child(3) {
      animation-delay: 0s;
    }
  }
}

@keyframes loadingDot {
  0%,
  80%,
  100% {
    transform: scale(0);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

// 语音输入区域
.voice-input-area {
  margin-top: 16px;
  padding-top: 16px;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}

// 识别文字显示区域 - 放在上半部分
.recognized-text {
  background: var(--bg-glass);
  border: 1px solid var(--border-glass);
  border-radius: 12px;
  padding: 12px 16px;
  color: var(--text-secondary);
  font-size: 24px; // 增加8px (原来16px)
  text-align: center;
  max-width: 80%;
  word-wrap: break-word;
  margin-bottom: 8px;
}

// 语音输入模式
.voice-chat-dialog .voice-input-mode {
  display: flex !important;
  flex-direction: column !important;
  align-items: center !important;
  justify-content: center !important;
  width: 100% !important;
  gap: 20px !important;
  position: relative !important;

  // 语音提示文字
  .voice-prompt-text {
    color: var(--text-tertiary) !important;
    font-size: 28px !important; // 增加8px (原来20px)
    font-weight: 500 !important;
    text-align: center !important;
    line-height: 1.2 !important;
  }

  // 右侧键盘切换按钮（>符号）
  .keyboard-toggle-btn {
    position: absolute !important;
    right: 0 !important;
    top: 50% !important;
    transform: translateY(-50%) !important;
    width: 32px !important;
    height: 32px !important;
    border: none !important;
    background: transparent !important;
    cursor: pointer !important;
    transition: all 0.3s ease !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;

    .chevron-right {
      font-size: 44px !important; // 增加8px (原来36px)
      color: var(--accent-color) !important;
    }
  }

  // 语音按钮（中间按钮）
  .voice-btn {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    border: 2px solid var(--primary-color);
    background: var(--bg-glass);
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    position: relative;
    backdrop-filter: blur(20px);
    box-shadow: var(--shadow-soft);

    // 录音状态：变大并且有动态光晕
    &.recording {
      width: 140px;
      height: 140px;
      border-color: var(--accent-color);
      background: var(--accent-color-light);
      animation: voiceGlow 2s ease-in-out infinite;
    }

    .voice-button-bg {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      border-radius: 50%;
      transition: all 0.3s ease;

      &.recording {
        background: var(--accent-color-medium);
      }
    }

    .voice-mic-icon {
      width: 48px;
      height: 48px;
      position: relative;
      z-index: 2;
    }
  }
}

// 键盘输入模式 - 使用更高的特异性来覆盖 Vant 样式
.dialog-overlay .dialog-container.voice-chat-dialog .keyboard-input-mode {
  display: flex !important;
  align-items: center !important;
  width: 100% !important;
  gap: 16px !important;

  .text-input-container {
    flex: 1 !important;
    display: flex !important;
    align-items: center !important;

    .text-input {
      width: 100% !important;
      height: 56px !important;
      background: var(--bg-glass) !important;
      border: 2px solid var(--border-glass) !important;
      border-radius: 16px !important;
      padding: 0 20px !important;
      color: var(--text-secondary) !important;
      font-size: 28px !important; // 增加8px (原来20px)
      max-height: 165px !important;
      font-weight: 600 !important;
      outline: none !important;
      transition: all 0.3s ease !important;
      backdrop-filter: blur(10px) !important;
      box-shadow: var(--shadow-accent) !important;

      &::placeholder {
        color: var(--text-disabled) !important;
      }

      &:focus {
        border-color: var(--border-accent) !important;
        background: var(--primary-color-light) !important;
        box-shadow: var(--shadow-strong) !important;
      }
    }
  }

  .voice-toggle-btn-circle {
    width: 56px !important;
    height: 56px !important;
    border-radius: 50% !important;
    border: 2px solid var(--border-glass) !important;
    background: var(--primary-color-light) !important;
    cursor: pointer !important;
    transition: all 0.3s ease !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    position: relative !important;
    backdrop-filter: blur(10px) !important;
    box-shadow: var(--shadow-accent) !important;
    flex-shrink: 0 !important;

    &:hover {
      background: var(--primary-color-light);
      border-color: var(--border-accent);
      transform: translateY(-2px);
      box-shadow: var(--shadow-strong);
    }

    &:active {
      transform: scale(0.95);
    }

    .voice-button-bg {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      border-radius: 50%;
      transition: all 0.3s ease;
    }

    .voice-mic-icon {
      width: 24px;
      height: 24px;
      filter: brightness(0) saturate(100%) invert(100%) sepia(0%) saturate(0%) hue-rotate(0deg)
        brightness(100%) contrast(100%);
      position: relative;
      z-index: 2;
    }
  }

  .send-btn {
    width: 70px;
    height: 70px;
    flex-shrink: 0;
    background: var(--primary-color-light);
    border: 2px solid var(--primary-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 10px;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: var(--shadow-strong);

    &.not-input {
      background: var(--bg-glass);
      border-color: var(--primary-color);
      .iconfont {
        color: var(--text-disabled);
      }
    }

    &.loading-input {
      background: var(--accent-color-light);
      border-color: var(--accent-color);
      .iconfont {
        font-size: 60px;
        color: var(--accent-color);
      }
    }

    &:active {
      transform: scale(0.95);
    }
  }

  .send-icon {
    border: none;
    background: none;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 8px;
    max-width: 46px;
    max-height: 46px;
  }
}

// 语音按钮光晕动画
@keyframes voiceGlow {
  0% {
    box-shadow:
      0 0 0 0 var(--accent-color-strong),
      var(--shadow-soft);
  }
  50% {
    box-shadow:
      0 0 0 15px var(--accent-color-light),
      var(--shadow-accent);
  }
  100% {
    box-shadow:
      0 0 0 30px transparent,
      var(--shadow-soft);
  }
}

// 动画
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideInScale {
  from {
    opacity: 0;
    transform: scale(0.9) translateY(-20px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}
</style>
