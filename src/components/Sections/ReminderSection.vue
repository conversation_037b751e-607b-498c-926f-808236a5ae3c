<template>
  <div class="reminder-section" :class="{ expanded: isReminderExpanded }">
    <div class="section-header">
      <ReminderIcon class="section-icon" width="40" height="40" color="var(--primary-color)" />
      <span class="section-title">提醒事项</span>
      <div class="section-actions">
        <button class="section-tts-btn" title="朗读内容" @click="handleTtsClick">
          <TrumpetIcon class="section-tts-icon" :size="'32px'" />
        </button>
        <button class="section-add-btn" title="添加" @click="handleAddReminder">
          <PlusIcon class="add-icon" :size="'32px'" />
        </button>
      </div>
    </div>
    <div class="section-content" :class="{ expanded: isReminderExpanded }">
      <div v-if="loadingReminders" class="loading-text">加载中...</div>
      <div v-else-if="reminders.length === 0" class="reminder-content">
        <div class="empty-reminder">快来添加提醒事项！</div>
      </div>
      <div v-else class="reminder-list" :class="{ expanded: isReminderExpanded }">
        <!-- 提醒项容器 -->
        <div class="reminder-items-container">
          <!-- 展开时显示全部，收起时只显示一条 -->
          <div
            v-for="reminder in isReminderExpanded ? reminders : reminders.slice(0, 1)"
            :key="reminder.reminder_id"
            class="reminder-item"
            @click="handleEditReminder(reminder)"
          >
            <div class="reminder-info">
              <div class="reminder-text">
                {{ reminder.display_text || '提醒事项' }}
              </div>
            </div>
            <div class="reminder-actions">
              <button class="delete-reminder-btn" @click.stop="handleDeleteReminder(reminder)">
                <DeleteIcon :size="16" color="var(--primary-color)" />
              </button>
            </div>
          </div>
        </div>

        <!-- 底部展开/收起按钮 - 当超过一个item时常驻显示 -->
        <div v-if="reminders.length > 1" class="content-footer">
          <span class="expand-toggle-text" @click="toggleReminderExpanded">
            {{ isReminderExpanded ? '收起' : '展开更多' }}
            <span class="arrow-icon" :class="{ expanded: isReminderExpanded }">{{
              isReminderExpanded ? '↑' : '↓'
            }}</span>
          </span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch, onBeforeUnmount } from 'vue';
import type { IReminder, IPersonDetail } from '@/apis/memory';
import { getReminders } from '@/apis/memory';
import { useAudioQueue } from '@/pages/Chat/useAudioPlayer';
import TrumpetIcon from '@/assets/icons/TrumpetIcon.vue';
import PlusIcon from '@/assets/icons/PlusIcon.vue';
import DeleteIcon from '@/assets/icons/DeleteIcon.vue';
import ReminderIcon from '@/assets/icons/ReminderIcon.vue';

// Props定义
interface IProps {
  personDetail: IPersonDetail | null;
  personId: string;
  userId: string;
  isUserProfile: boolean;
}

const props = defineProps<IProps>();

// Emits定义
const emit = defineEmits<{
  addReminder: [];
  editReminder: [reminder: IReminder];
  deleteReminder: [reminder: IReminder];
}>();

// TTS相关
const { play, stop, isCurrentAudioPlaying, audioStatus } = useAudioQueue();
const isTtsPlaying = ref(false);
const ttsId = 'reminder-section-tts';

// 响应式数据
const isReminderExpanded = ref(false);
const reminders = ref<IReminder[]>([]);
const loadingReminders = ref(false);

// 切换提醒事项展开/收起状态
const toggleReminderExpanded = () => {
  isReminderExpanded.value = !isReminderExpanded.value;
};

// 处理添加提醒
const handleAddReminder = () => {
  emit('addReminder');
};

// 处理编辑提醒
const handleEditReminder = (reminder: IReminder) => {
  emit('editReminder', reminder);
};

// 处理删除提醒 - 触发删除事件
const handleDeleteReminder = (reminder: IReminder) => {
  console.log('🗑️ [ReminderSection] 触发删除提醒事件:', reminder);
  // 触发父组件的删除事件，让父组件处理确认弹窗和实际删除逻辑
  emit('deleteReminder', reminder);
};

// 加载提醒数据
const loadReminders = async () => {
  console.log('🔄 [ReminderSection] loadReminders调用，参数:', {
    userId: props.userId,
    isUserProfile: props.isUserProfile,
    personId: props.personId,
  });

  if (!props.userId || !props.isUserProfile) {
    console.log('⚠️ [ReminderSection] 不满足加载条件，清空提醒数据');
    reminders.value = [];
    return;
  }

  try {
    loadingReminders.value = true;
    console.log('🔄 [ReminderSection] 开始获取提醒数据...');

    const response = await getReminders({ user_id: props.userId });
    console.log('📡 [ReminderSection] 提醒数据响应:', response);

    if (response && response.success) {
      // 倒序排列，后添加的在上方
      reminders.value = (response.reminders || []).reverse();
      console.log('✅ [ReminderSection] 提醒数据加载成功，共', reminders.value.length, '条提醒');
    } else {
      console.warn('⚠️ [ReminderSection] 提醒数据格式异常:', response);
      reminders.value = [];
    }
  } catch (error) {
    console.error('❌ [ReminderSection] 获取提醒数据失败:', error);
    reminders.value = [];
  } finally {
    loadingReminders.value = false;
  }
};

// TTS朗读处理
const handleTtsClick = () => {
  if (isCurrentAudioPlaying(ttsId)) {
    stop();
    isTtsPlaying.value = false;
  } else {
    // 构建朗读内容：读出每个提醒事项的display_text
    const ttsContent = reminders.value.map((reminder) => reminder.display_text).join('。');

    if (ttsContent.trim()) {
      isTtsPlaying.value = true;
      play({
        id: ttsId,
        text: ttsContent,
        type: 'manualPlay',
      });

      // 监听播放状态变化
      const checkPlayingStatus = () => {
        if (!isCurrentAudioPlaying(ttsId) && audioStatus.value === 'completed') {
          isTtsPlaying.value = false;
        } else {
          setTimeout(checkPlayingStatus, 100);
        }
      };
      checkPlayingStatus();
    }
  }
};

// 监听自定义事件，刷新数据
const handleAddReminderSuccess = () => {
  console.log('🔄 [ReminderSection] 收到添加提醒成功事件，刷新数据');
  void loadReminders();
};

const handleEditReminderSuccess = () => {
  console.log('🔄 [ReminderSection] 收到编辑提醒成功事件，刷新数据');
  void loadReminders();
};

// 监听props变化，重新加载数据
watch(
  () => [props.personId, props.userId, props.isUserProfile],
  () => {
    void loadReminders();
  },
  { immediate: true },
);

// 组件挂载时监听自定义事件
onMounted(() => {
  window.addEventListener('addremindersuccess', handleAddReminderSuccess);
  window.addEventListener('editremindersuccess', handleEditReminderSuccess);
});

// 组件卸载时移除事件监听
onBeforeUnmount(() => {
  window.removeEventListener('addremindersuccess', handleAddReminderSuccess);
  window.removeEventListener('editremindersuccess', handleEditReminderSuccess);
});

// 组件挂载时加载数据
onMounted(() => {
  void loadReminders();
});

// 暴露刷新方法给父组件
defineExpose({
  loadReminders,
});
</script>

<style lang="scss" scoped>
.reminder-section {
  border: none;
  border-radius: 16px;
  padding: 22px 22px 22px 28px; /* 增加左侧padding从22px到28px */
  margin-top: 24px;
  background: var(--bg-glass);
  backdrop-filter: blur(10px);
  border-left: 4px solid var(--accent-color);
  box-shadow: var(--shadow-accent);
  display: flex;
  flex-direction: column;
}

// 提醒事项展开/收起控制 - 修改为固定高度并将展开按钮固定到底部
.reminder-section .section-content {
  display: flex;
  flex-direction: column;

  &.expanded {
    min-height: auto; // 展开时取消最小高度限制
  }
}

// 提醒内容区域
.reminder-section .reminder-list {
  flex: 1;
  overflow: hidden;

  &.expanded {
    overflow: visible;
  }
}

// 底部按钮区域固定到底部
.reminder-section .content-footer {
  margin-top: auto; // 自动推到底部
  flex-shrink: 0; // 防止被压缩
}

.section-header {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
  gap: 8px;

  .section-icon {
    font-size: 40px;
    width: 40px;
    height: 40px;
    fill: var(--primary-color);
  }

  .section-title {
    color: var(--primary-color);
    font-size: 34px;
    font-weight: 600;
    flex: 1;
  }

  .section-actions {
    display: flex;
    align-items: center;
    gap: 14px;
  }
}

.section-add-btn {
  width: 42px;
  height: 42px;
  border-radius: 50%;
  border: 2px solid var(--primary-color);
  background: transparent;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;

  .add-icon {
    width: 32px;
    height: 32px;
    min-width: 32px;
    min-height: 32px;
    transition: all 0.3s ease;
  }
}

.section-tts-btn {
  width: 42px;
  height: 42px;
  border-radius: 50%;
  border: 2px solid var(--primary-color);
  background: transparent;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;

  .section-tts-icon {
    width: 32px;
    height: 32px;
    min-width: 32px;
    min-height: 32px;
  }
}

.section-content {
  color: var(--text-primary); //
  line-height: 1.6;
  flex: 1;
  overflow-y: auto;
  min-height: 0;
}

.loading-text {
  color: var(--text-disabled);
  font-style: italic;
  text-align: center;
  padding: 10px 0;
  font-size: 32px;
}

//
.reminder-list {
  display: flex;
  flex-direction: column;

  //
  .reminder-items-container {
    flex: 1;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    gap: 20px;

    &.expanded {
      overflow: visible;
    }
  }
}

.reminder-item {
  background: var(--primary-color-light);
  border: 2px solid var(--border-accent);
  border-radius: 16px;
  padding: 20px;
  position: relative;
  cursor: pointer;
  transition: all 0.3s ease;
}

.reminder-info {
  flex: 1;
  padding-right: 40px;

  .reminder-text {
    color: var(--person-detail-context);
    font-size: 32px;
    font-weight: 450;
    margin-bottom: 12px;
  }
}

.reminder-actions {
  position: absolute;
  top: 4px;
  right: 4px;
}

.delete-reminder-btn {
  background: none;
  border: none;
  cursor: pointer;
  color: var(--accent-color);
  padding: 4px 8px;
  border-radius: 4px;
  transition: all 0.2s ease;
  font-size: 18px;
  line-height: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

.reminder-content {
  .empty-reminder {
    color: var(--text-primary);
    font-size: 30px;
    font-style: italic;
    text-align: center;
    padding: 20px 0;
    background: var(--primary-color-light);
    border: 1px dashed var(--border-accent);
    border-radius: 12px;
    line-height: 1.6;
  }
}

// 底部展开/收起文字样式
.content-footer {
  display: flex;
  justify-content: center;
  margin-top: 20px;
  padding-top: 16px;
  border-top: 1px solid var(--border-accent);
}

.expand-toggle-text {
  color: var(--primary-color);
  font-size: 28px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 6px;

  .arrow-icon {
    font-size: 24px;
    transition: all 0.3s ease;
  }
}
</style>
