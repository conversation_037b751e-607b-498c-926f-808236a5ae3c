<template>
  <div class="history-sidebar-container">
    <!-- 遮罩层 -->
    <div v-if="isOpen" class="sidebar-overlay" @click="handleClose"></div>
    <!-- 侧边栏 -->
    <div class="history-sidebar" :class="{ 'sidebar-open': isOpen }">
      <div class="sidebar-header">
        <div class="title">历史对话</div>
        <div class="close-btn" @click="handleClose">
          <DeleteIcon :size="20" color="var(--primary-color)" />
        </div>
      </div>
      <div class="sidebar-content">
        <div v-if="loading" class="loading">加载中...</div>
        <div v-else-if="Object.keys(conversationSummary).length === 0" class="empty-state">
          暂无历史会话
        </div>
        <div v-else class="conversation-list">
          <div
            v-for="conversationId in Object.keys(conversationSummary)"
            :key="conversationId"
            class="conversation-item"
            :class="{
              'current-conversation': conversationSummary[conversationId] === props.currentTitle,
            }"
          >
            <div class="conversation-content" @click="handleConversationClick(conversationId)">
              <div class="conversation-title">
                {{ conversationSummary[conversationId] }}
              </div>
              <div class="conversation-info">
                <span class="conversation-time">{{ getConversationTime(conversationId) }}</span>
              </div>
            </div>
            <div class="conversation-actions">
              <div class="delete-btn" @click.stop="handleDeleteConversation(conversationId)">
                <DeleteIcon :size="20" color="var(--primary-color)" />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- 删除确认对话框 -->
  <DeleteConfirmDialog
    :visible="showDeleteDialog"
    :content="`确定要删除对话 ${conversationToDelete?.title} 吗？`"
    hint="删除后将无法恢复该对话的历史记录"
    :is-loading="isDeleting"
    @confirm="confirmDeleteConversation"
    @cancel="closeDeleteDialog"
  />
</template>

<script setup lang="ts">
import { watch, ref, onMounted, onBeforeMount } from 'vue';
import {
  getConversationList,
  getConversationHistory,
  getUserInfo,
  deleteConversation,
} from '@/apis/common';
import { showSuccessToast, showFailToast } from 'vant';
import DeleteConfirmDialog from '@/components/Common/DeleteConfirmDialog.vue';
import DeleteIcon from '@/assets/icons/DeleteIcon.vue';

const props = defineProps({
  isOpen: {
    type: Boolean,
    default: false,
  },
  currentTitle: {
    type: String,
    default: '',
  },
});

const emit = defineEmits(['close', 'select-conversation']);

const conversationSummary = ref<Record<string, string>>({});
const loading = ref(false);
const userId = ref('');

// 删除相关状态
const showDeleteDialog = ref(false);
const isDeleting = ref(false);
const conversationToDelete = ref<{ id: string; title: string } | null>(null);

// 初始化用户ID
onBeforeMount(async () => {
  try {
    const userInfo = await getUserInfo();
    if (userInfo && userInfo.login) {
      userId.value = userInfo.login;
    } else {
      userId.value = 'unknown_user';
    }
  } catch (error) {
    userId.value = 'unknown_user';
  }
});

// 获取会话时间
const getConversationTime = (conversationId: string): string => {
  try {
    // 尝试从会话ID中提取时间信息
    // 格式1: "user123_2024-01-01-10-30-45"
    const parts = conversationId.split('_');
    if (parts.length >= 2) {
      const datePart = parts[1];
      // 检查是否符合日期格式
      if (/^\d{4}-\d{2}-\d{2}-\d{2}-\d{2}/.test(datePart)) {
        // 将格式转换为更友好的显示: "2024-01-01-10-30-45" -> "2024/01/01 10:30"
        const dateStr = datePart.replace(/-/g, '/');
        const dateTimeMatch = dateStr.match(/(\d{4}\/\d{2}\/\d{2})\/(\d{2})\/(\d{2})/);
        if (dateTimeMatch) {
          return `${dateTimeMatch[1]} ${dateTimeMatch[2]}:${dateTimeMatch[3]}`;
        }
        return dateStr;
      }
    }

    // 如果没有明确的时间格式，使用当前时间
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const day = String(now.getDate()).padStart(2, '0');
    const hours = String(now.getHours()).padStart(2, '0');
    const minutes = String(now.getMinutes()).padStart(2, '0');

    return `${year}/${month}/${day} ${hours}:${minutes}`;
  } catch (error) {
    console.error('解析会话时间出错:', error);
    return '最近会话';
  }
};

// 获取历史会话记录
const fetchConversationList = async () => {
  if (!userId.value) {
    return;
  }

  try {
    loading.value = true;
    const response = await getConversationList(userId.value);

    if (response?.success && response?.summary && typeof response.summary === 'object') {
      // 新的后端响应格式：summary是一个键值对对象
      // key是conversation_id，value是对话标题
      conversationSummary.value = response.summary;
    } else {
      conversationSummary.value = {};
    }
  } catch (error) {
    conversationSummary.value = {};
  } finally {
    loading.value = false;
  }
};

// 关闭侧边栏
const handleClose = () => {
  emit('close');
};

// 处理会话点击事件
const handleConversationClick = async (conversationId: string) => {
  try {
    const response = await getConversationHistory(conversationId, userId.value);

    if (response && response.status === 'success') {
      // 转换历史消息格式为兼容的格式
      const messageList = response.history.map((msg) => ({
        role: msg.type === 'human' ? 'user' : 'assistant',
        content: msg.content,
        reasoning_content: '', // 历史记录中没有推理内容
      }));

      // 构造兼容的会话数据格式
      const conversationData = {
        conversationId: response.conversation_id,
        title: conversationSummary.value[conversationId] || '历史会话',
        messageList,
      };

      // 将会话数据存储到sessionStorage，避免通过事件传递大量数据
      sessionStorage.setItem('pendingConversationRestore', JSON.stringify(conversationData));

      // 先发送选择事件
      emit('select-conversation', conversationData);
      // 关闭侧边栏
      emit('close');
    } else {
      console.warn('⚠️ [historySidebar] 历史记录响应状态异常:', response);
    }
  } catch (error) {
    console.error('❌ [historySidebar] 获取历史聊天记录失败:', error);
  }
};

// 监听侧边栏打开状态
watch(
  () => props.isOpen,
  async (newVal) => {
    if (newVal) {
      document.body.style.overflow = 'hidden';
      await fetchConversationList(); // 打开侧边栏时获取历史会话记录
    } else {
      document.body.style.overflow = '';
    }
  },
);

onMounted(async () => {
  if (props.isOpen) {
    await fetchConversationList();
  }
});

// 处理删除对话
const handleDeleteConversation = (conversationId: string) => {
  const title = conversationSummary.value[conversationId] || '未知对话';
  conversationToDelete.value = { id: conversationId, title };
  showDeleteDialog.value = true;
};

// 关闭删除确认对话框
const closeDeleteDialog = () => {
  showDeleteDialog.value = false;
  conversationToDelete.value = null;
};

// 确认删除对话
const confirmDeleteConversation = async () => {
  if (!conversationToDelete.value || !userId.value) {
    return;
  }

  try {
    isDeleting.value = true;
    console.log('🔄 [historySidebar] 开始删除对话...', {
      userId: userId.value,
      conversationId: conversationToDelete.value.id,
      title: conversationToDelete.value.title,
    });

    const response = await deleteConversation({
      user_id: userId.value,
      conversation_id: conversationToDelete.value.id,
    });

    console.log('📡 [historySidebar] 删除对话响应:', response);

    if (response && response.success) {
      console.log('✅ [historySidebar] 对话删除成功');
      showSuccessToast('删除成功');

      // 从本地列表中移除已删除的对话
      const { [conversationToDelete.value.id]: deleted, ...remaining } = conversationSummary.value;
      conversationSummary.value = remaining;

      // 关闭删除对话框
      closeDeleteDialog();
    } else {
      console.warn('⚠️ [historySidebar] 删除对话失败:', response);
      showFailToast(response?.message || '删除对话失败');
    }
  } catch (error) {
    console.error('❌ [historySidebar] 删除对话失败:', error);
    showFailToast('删除对话失败');
  } finally {
    isDeleting.value = false;
  }
};
</script>

<style lang="scss" scoped>
@import '@/styles/util.scss';
@import '@/styles/variable.scss';

.history-sidebar-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1000;
  pointer-events: none;
}

.history-sidebar {
  position: absolute;
  top: 0;
  left: 0;
  width: 60%;
  height: 100%;
  background: var(--bg-glass-popup);
  backdrop-filter: blur(20px);
  box-shadow:
    var(--shadow-strong),
    0 0 0 1px var(--border-light);
  z-index: 1001;
  transform: translateX(-100%);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  pointer-events: auto;

  &.sidebar-open {
    transform: translateX(0);
  }

  .sidebar-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-xl) var(--spacing-xl) var(--spacing-lg) var(--spacing-xl);
    background: transparent;
    border-bottom: 1px solid var(--border-light);
    position: relative;

    &::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: var(--spacing-xl);
      right: var(--spacing-xl);
      height: 1px;
      background: linear-gradient(90deg, transparent 0%, var(--border-light) 50%, transparent 100%);
    }

    .title {
      font-size: var(--font-size-3xl);
      font-weight: 600;
      color: var(--primary-color);
      letter-spacing: -0.5px;
    }

    .close-btn {
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 40px;
      height: 40px;
      border-radius: var(--border-radius-sm);
      background: transparent;
      border: 1px solid transparent;
      color: var(--text-tertiary);
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

      &:hover {
        background: var(--bg-hover);
        border-color: var(--border-light);
        color: var(--text-primary);
        transform: scale(1.05);
        box-shadow: var(--shadow-medium);
      }

      &:active {
        transform: scale(0.95);
      }

      img {
        width: 20px;
        height: 20px;
        opacity: 0.7;
        transition: opacity 0.3s ease;
      }

      &:hover img {
        opacity: 1;
      }
    }
  }

  .sidebar-content {
    flex: 1;
    overflow-y: auto;
    padding: var(--spacing-md) var(--spacing-lg) var(--spacing-lg) var(--spacing-lg);
    background-color: transparent;
    /* 隐藏滚动条但保持可滚动 */
    &::-webkit-scrollbar {
      display: none;
    }
    -ms-overflow-style: none;
    scrollbar-width: none;

    .loading,
    .empty-state {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      height: 200px;
      color: var(--primary-color);
      font-size: var(--font-size-2xl);
      font-weight: 500;
    }

    .conversation-list {
      display: flex;
      flex-direction: column;
      gap: var(--spacing-sm);

      .conversation-item {
        padding: var(--spacing-lg);
        border-radius: var(--border-radius-md);
        background: var(--bg-glass);
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        border: 1px solid var(--border-light);
        backdrop-filter: blur(10px);
        position: relative;
        overflow: hidden;
        display: flex;
        align-items: center;
        justify-content: space-between;

        &::before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          height: 3px;
          background: linear-gradient(90deg, #3b82f6 0%, #1d4ed8 50%, #1e40af 100%);
          opacity: 0;
          transition: opacity 0.3s ease;
        }

        &.current-conversation {
          background: linear-gradient(
            135deg,
            rgba(99, 102, 241, 0.08) 0%,
            rgba(139, 92, 246, 0.08) 100%
          );
          border-color: rgba(99, 102, 241, 0.2);
          box-shadow: 0 4px 16px rgba(99, 102, 241, 0.1);

          &::before {
            opacity: 1;
          }
        }

        &:hover {
          background: linear-gradient(
            135deg,
            rgba(255, 255, 255, 0.1) 0%,
            rgba(248, 250, 252, 0.15) 100%
          );
          border-color: rgba(255, 255, 255, 0.2);
          transform: translateY(-2px);
          box-shadow: 0 8px 24px rgba(0, 0, 0, 0.08);

          .delete-btn {
            opacity: 1;
          }

          &.current-conversation {
            background: linear-gradient(
              135deg,
              rgba(99, 102, 241, 0.15) 0%,
              rgba(139, 92, 246, 0.15) 100%
            );
            border-color: rgba(99, 102, 241, 0.3);
            box-shadow: 0 8px 24px rgba(99, 102, 241, 0.15);
          }
        }

        &:active {
          transform: translateY(-1px);
        }

        .conversation-title {
          font-size: 30px; // 增加4px
          font-weight: 500;
          color: var(--person-detail-context);
          margin-bottom: 8px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          line-height: 1.4;
          letter-spacing: -0.2px;
          max-width: 100%; // 确保标题不会超出容器
        }

        .conversation-info {
          display: flex;
          justify-content: space-between;
          align-items: center;

          .conversation-time {
            font-family: 'SF Mono', 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 20px;
            color: var(--primary-color-timestamp);
            background: var(--bg-glass);
            padding: 4px 8px;
            border-radius: 6px;
            border: 1px solid var(--border-light);
            font-weight: 500;
          }
        }

        .conversation-content {
          flex: 1;
          cursor: pointer;
          min-width: 0; // 允许flex子元素收缩
          overflow: hidden; // 防止内容溢出
        }

        .conversation-actions {
          display: flex;
          align-items: center;
          margin-left: 12px;
          flex-shrink: 0; // 防止按钮被压缩

          .delete-btn {
            width: 40px; // 增加按钮尺寸
            height: 40px; // 增加按钮尺寸
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px; // 相应调整圆角
            cursor: pointer;
            transition: all 0.3s ease;
            opacity: 0.7;
            
            img {
              width: 20px; // 增加图标尺寸
              height: 20px; // 增加图标尺寸
              filter: brightness(0) saturate(100%) invert(27%) sepia(51%) saturate(2878%)
                hue-rotate(346deg) brightness(104%) contrast(97%);
            }
          }
        }
      }
    }
  }
}

.sidebar-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--overlay-dark);
  backdrop-filter: blur(8px);
  z-index: 1000;
  width: 100%;
  height: 100%;
  pointer-events: auto; /* 恢复点击事件，确保可以点击遮罩层关闭侧边栏 */
  transition: all 0.3s ease;
}

/* 添加一些微动画效果 */
@keyframes slideInLeft {
  from {
    transform: translateX(-100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.history-sidebar.sidebar-open {
  animation: slideInLeft 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.sidebar-overlay {
  animation: fadeIn 0.3s ease;
}

// 删除确认对话框样式 - 与PersonDetailPopup保持一致
// 移到容器外部，使用更高的z-index确保在所有元素之上
.dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(20px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1002; // 使用更高的z-index确保在所有元素之上
  animation: fadeIn 0.3s ease-out;
  pointer-events: auto; // 确保可以点击
}

.dialog-container {
  background: rgba(1, 28, 32, 0.6);
  border: none;
  border-radius: 16px;
  padding: 30px;
  box-sizing: border-box;
  backdrop-filter: blur(10px);
  border-left: 4px solid #00ffff;
  box-shadow: -4px 0 8px rgba(0, 255, 255, 0.3);
  transition: all 0.3s ease;
  width: 90%;
  max-width: 500px;
  color: white;
  animation: slideInScale 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
  display: flex;
  flex-direction: column;
  pointer-events: auto; // 确保对话框容器可以接收点击事件
}

.dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 18px;
  padding-bottom: 18px;
  position: relative;

  .dialog-title {
    color: rgba(255, 255, 255, 0.9);
    font-size: 32px; // 与PersonDetailPopup一致
    font-weight: 600;
  }

  .dialog-close {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    width: 48px; // 与PersonDetailPopup一致
    height: 48px; // 与PersonDetailPopup一致
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    transition: all 0.3s ease;

    &:hover {
      background: rgba(255, 255, 255, 0.2);
      border-color: rgba(255, 255, 255, 0.4);
      transform: scale(1.1);
    }

    .close-icon {
      width: 24px; // 与PersonDetailPopup一致
      height: 24px; // 与PersonDetailPopup一致
      filter: brightness(0) invert(1);
    }
  }
}

.dialog-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 32px;

  .delete-warning {
    color: #ffffff;
    font-size: 32px; // 与PersonDetailPopup一致
    font-weight: 600;
    line-height: 1.4;
    margin: 0 0 12px 0;
    text-align: center;

    strong {
      color: #ef4444;
    }
  }

  .delete-hint {
    color: rgba(255, 255, 255, 0.7);
    font-size: 24px; // 与PersonDetailPopup一致
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    text-align: center;
  }
}

.dialog-footer {
  display: flex;
  gap: 20px;
  margin-top: 40px;
  padding-top: 32px;
  border-top: 1px solid rgba(255, 255, 255, 0.2);

  .cancel-btn,
  .delete-confirm-btn {
    flex: 1;
    padding: 16px 16px;
    border-radius: 20px;
    font-size: 28px; // 与PersonDetailPopup一致
    font-weight: 600;
    cursor: pointer;
    border: 2px solid;
    background: transparent;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 60px;
    width: 200px;

    &:disabled {
      opacity: 0.6;
      cursor: not-allowed;
    }
  }

  .cancel-btn {
    color: rgba(255, 255, 255, 0.8);
    border-color: rgba(255, 255, 255, 0.8);

    &:hover:not(:disabled) {
      background: rgba(255, 255, 255, 0.1);
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(255, 255, 255, 0.3);
    }
  }

  .delete-confirm-btn {
    color: #ef4444;
    border-color: #ef4444;

    &:hover:not(:disabled) {
      background: rgba(239, 68, 68, 0.1);
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);
    }
  }
}

@keyframes slideInScale {
  from {
    opacity: 0;
    transform: scale(0.9) translateY(20px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}
</style>
