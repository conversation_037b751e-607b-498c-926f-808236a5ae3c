<template>
  <div class="memory-list-container">
    <!-- 头部标题 -->
    <div class="memory-header">
      <div class="memory-icon">📝</div>
      <div class="memory-title">随手记</div>
      <div class="memory-close" @click="$emit('close')">
        <DeleteIcon :size="20" color="var(--primary-color)" />
      </div>
    </div>

    <!-- 记忆列表 -->
    <div class="memory-content">
      <div v-if="loading" class="loading-container">
        <div class="loading-spinner"></div>
        <div class="loading-text">加载中...</div>
      </div>

      <div v-else-if="memories.length === 0" class="empty-container">
        <div class="empty-text">暂无记忆记录</div>
      </div>

      <div v-else class="memory-items">
        <div v-for="memory in memories" :key="memory.event_id" class="memory-item">
          <div class="memory-header">
            <div class="memory-date">{{ formatDate(memory.timestamp) }}</div>
          </div>
          <div class="memory-description">{{ memory.description_text }}</div>
          <div v-if="memory.location" class="memory-location">
            <span class="location-icon">📍</span>
            {{ memory.location }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { getPersonMemories, type IEvent } from '@/apis/memory';
import { showFailToast } from 'vant';
import DeleteIcon from '@/assets/icons/DeleteIcon.vue';

// Props定义
interface IProps {
  userId: string;
  personId: string;
  personName: string;
}

const props = defineProps<IProps>();

// Emits定义
const emit = defineEmits<{
  close: [];
}>();

// 响应式数据
const loading = ref(true);
const memories = ref<IEvent[]>([]);

// 格式化日期
const formatDate = (timestamp: string): string => {
  const date = new Date(timestamp);
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
  });
};

// 获取记忆数据
const loadMemories = async () => {
  try {
    loading.value = true;
    console.log('🔄 [memoryList.vue] 开始获取记忆数据...', {
      userId: props.userId,
      personId: props.personId,
      personName: props.personName,
    });

    // 检查必要参数
    if (!props.userId || !props.personId) {
      console.error('❌ [memoryList.vue] 缺少必要参数:', {
        userId: props.userId,
        personId: props.personId,
      });
      memories.value = [];
      return;
    }

    const requestParams = {
      user_id: props.userId,
      person_id: props.personId,
    };
    console.log('📤 [memoryList.vue] API请求参数:', requestParams);

    const response = await getPersonMemories(requestParams);

    console.log('📡 [memoryList.vue] 记忆数据响应:', response);
    console.log('📡 [memoryList.vue] 响应类型检查:', {
      hasResponse: !!response,
      hasResult: response?.result,
      resultValue: response?.result,
      hasEvents: !!response?.events,
      eventsType: typeof response?.events,
      eventsLength: Array.isArray(response?.events) ? response.events.length : 'not array',
      eventsContent: response?.events,
    });

    if (response && response.result === 'success' && response.events) {
      memories.value = response.events;
      console.log('✅ [memoryList.vue] 记忆数据加载成功，共', memories.value.length, '条记忆');
      console.log('✅ [memoryList.vue] 记忆数据详情:', memories.value);
    } else {
      console.warn('⚠️ [memoryList.vue] 记忆数据格式异常，详细信息:', {
        response,
        hasResponse: !!response,
        result: response?.result,
        hasEvents: !!response?.events,
        events: response?.events,
      });
      memories.value = [];
    }
  } catch (error) {
    console.error('❌ [memoryList.vue] 获取记忆数据失败:', error);
    console.error('❌ [memoryList.vue] 错误详情:', {
      message: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined,
      error,
    });
    showFailToast('获取记忆数据失败');
    memories.value = [];
  } finally {
    loading.value = false;
  }
};

// 组件挂载时加载数据
onMounted(() => {
  void loadMemories();
});
</script>

<style lang="scss" scoped>
.memory-list-container {
  background: rgba(1, 28, 32, 0.6);
  border: none;
  border-radius: 16px;
  padding: 30px;
  box-sizing: border-box;
  backdrop-filter: blur(10px);
  border-left: 4px solid #00ffff;
  box-shadow: -4px 0 8px rgba(0, 255, 255, 0.3);
  width: 480px;
  max-height: 640px;
  color: white;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  display: flex;
  flex-direction: column;
}

.memory-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding-bottom: 20px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  position: relative;

  .memory-icon {
    font-size: 32px; // 增加8px (原来24px)
    margin-right: 8px;
  }

  .memory-title {
    color: rgba(255, 255, 255, 0.8);
    font-size: 26px; // 增加8px (原来18px)
    font-weight: 500;
    flex: 1;
    display: flex;
    align-items: center;
  }

  .memory-close {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    color: rgba(255, 255, 255, 0.8);
    transition: all 0.3s ease;
    font-size: 28px; // 增加8px (原来20px)

    &:hover {
      background: rgba(255, 255, 255, 0.2);
      border-color: rgba(255, 255, 255, 0.4);
      color: #ffffff;
      transform: scale(1.1);
    }
  }
}

.memory-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 24px;
  overflow-y: auto;

  &::-webkit-scrollbar {
    width: 4px;
  }

  &::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 2px;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 2px;

    &:hover {
      background: rgba(255, 255, 255, 0.5);
    }
  }
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 40px 0;

  .loading-spinner {
    width: 24px;
    height: 24px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-top: 2px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 12px;
  }

  .loading-text {
    color: #ffffff;
    font-size: 36px; // 增加8px (原来28px)
    font-weight: 700;
    line-height: 1.4;
  }
}

.empty-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40px 0;

  .empty-text {
    color: rgba(255, 255, 255, 0.7);
    font-size: 24px; // 增加8px (原来16px)
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }
}

.memory-items {
  display: flex;
  flex-direction: column;
  gap: 20px;

  .memory-item {
    display: flex;
    flex-direction: column;
    gap: 8px;

    .memory-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 8px;
      padding-bottom: 8px;
      border-bottom: 1px solid rgba(255, 255, 255, 0.2);

      .memory-date {
        color: rgba(255, 255, 255, 0.8);
        font-size: 26px; // 增加8px (原来18px)
        font-weight: 500;
      }
    }

    .memory-description {
      color: #ffffff;
      font-size: 36px; // 增加8px (原来28px)
      font-weight: 700;
      line-height: 1.4;
      margin: 0;
    }

    .memory-location {
      color: rgba(255, 255, 255, 0.7);
      font-size: 24px; // 增加8px (原来16px)
      font-weight: 600;
      text-transform: uppercase;
      letter-spacing: 0.5px;
      display: flex;
      align-items: center;
      gap: 4px;

      .location-icon {
        font-size: 22px; // 增加8px (原来14px)
      }
    }
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
</style>
