<template>
  <div class="avatar-upload-container">
    <!-- 头像显示区域 -->
    <div class="avatar-display" @click="handleAvatarClick">
      <div v-if="uploading" class="avatar-uploading">
        <div class="upload-progress">
          <div class="progress-circle">
            <div class="progress-text">{{ uploadProgress }}%</div>
          </div>
        </div>
      </div>
      <img v-else-if="avatarUrl" :src="avatarUrl" :alt="altText" class="avatar-image" />
      <div v-else class="avatar-placeholder" :style="{ backgroundColor: placeholderColor }">
        <i class="iconfont icon-camera upload-icon"></i>
        <span class="upload-text">上传头像</span>
      </div>
    </div>

    <!-- 隐藏的文件输入 -->
    <input
      ref="fileInput"
      type="file"
      accept="image/jpeg,image/jpg,image/png,image/gif,image/bmp,image/webp"
      style="display: none"
      @change="handleFileSelect"
    />

    <!-- 错误提示 -->
    <div v-if="errorMessage" class="error-message">
      {{ errorMessage }}
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { showToast } from 'vant';
import createUpload from '@ai/mss-upload-js';
import { getAvatarUrl } from '@/utils/avatarUtils';

// Props定义
interface IProps {
  modelValue?: string; // 当前头像URL
  size?: number; // 头像大小
  placeholder?: string; // 占位符文本
  placeholderColor?: string; // 占位符背景色
  altText?: string; // 图片alt文本
  maxSize?: number; // 最大文件大小(MB)
  disabled?: boolean; // 是否禁用
}

const props = withDefaults(defineProps<IProps>(), {
  modelValue: '',
  size: 80,
  placeholder: '上传头像',
  placeholderColor: '#4ecdc4',
  altText: '头像',
  maxSize: 10,
  disabled: false,
});

// Emits定义
const emit = defineEmits<{
  'update:modelValue': [value: string];
  'upload-success': [url: string];
  'upload-error': [error: string];
  'show-avatar-selection': [];
}>();

// 响应式数据
const fileInput = ref<HTMLInputElement>();
const uploading = ref(false);
const uploadProgress = ref(0);
const errorMessage = ref('');

// 计算属性
const avatarUrl = computed(() => getAvatarUrl(props.modelValue));

// 支持的图片格式
const supportedFormats = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'];

// 获取文件扩展名
const getFileExtension = (fileName: string): string => {
  return fileName.slice(fileName.lastIndexOf('.') + 1).toLowerCase();
};

// 验证文件格式
const validateFormat = (file: File): boolean => {
  const extension = getFileExtension(file.name);
  return supportedFormats.includes(extension);
};

// 验证文件大小
const validateSize = (file: File): boolean => {
  const fileSizeMB = file.size / 1024 / 1024;
  return fileSizeMB <= props.maxSize;
};

// 处理头像点击
const handleAvatarClick = () => {
  if (props.disabled || uploading.value) return;

  // 发射事件通知父组件显示头像选择弹窗
  emit('show-avatar-selection');
};

// 触发文件选择
const triggerUpload = () => {
  if (props.disabled || uploading.value) return;
  fileInput.value?.click();
};

// 处理文件选择
const handleFileSelect = (event: Event) => {
  const target = event.target as HTMLInputElement;
  const file = target.files?.[0];

  if (!file) return;

  // 清除之前的错误信息
  errorMessage.value = '';

  // 验证文件格式
  if (!validateFormat(file)) {
    errorMessage.value = `不支持的文件格式，请上传 ${supportedFormats.join('、')} 格式的图片`;
    showToast(errorMessage.value);
    target.value = '';
    return;
  }

  // 验证文件大小
  if (!validateSize(file)) {
    errorMessage.value = `文件大小不能超过 ${props.maxSize}MB`;
    showToast(errorMessage.value);
    target.value = '';
    return;
  }

  // 开始上传
  uploadFile(file);
  target.value = '';
};

// 上传文件到S3
const uploadFile = (file: File) => {
  uploading.value = true;
  uploadProgress.value = 0;
  errorMessage.value = '';

  const isPro = process.env.VUE_APP_ENV === 'production';
  const s3Host = isPro ? 's3plus-bj02.sankuai.com' : 'msstest.sankuai.com';

  const options = {
    file,
    prefix_type: 's3_style',
    signatureUrl: '/web/v1/s3/sign',
    bucket: 'aigc-public-resources',
    s3_host: s3Host,
    isHttps: true,
    uploadType: 1, // 单文件上传
    signAdaptor(val: { status: number; code: number; data: { key: string } }) {
      if (val.status === 0) {
        val.code = 0;
      }
      // 使用头像专用的key前缀
      const key = 'avatar/';
      val.data.key = key + val.data.key;
    },
    onSuccess: (fileUrl: string) => {
      console.log('✅ [AvatarUpload] 头像上传成功:', fileUrl);
      uploading.value = false;
      uploadProgress.value = 100;

      // 更新头像URL
      emit('update:modelValue', fileUrl);
      emit('upload-success', fileUrl);

      showToast('头像上传成功');
    },
    onError: (error: Error | string) => {
      console.error('❌ [AvatarUpload] 头像上传失败:', error);
      uploading.value = false;
      uploadProgress.value = 0;
      errorMessage.value = '头像上传失败，请重试';

      emit('upload-error', errorMessage.value);
      showToast(errorMessage.value);
    },
    onProgress: (progress: number) => {
      uploadProgress.value = Math.round(progress);
    },
  };

  const uploadInstance = createUpload(options, 1); // 使用单文件上传类型
  void uploadInstance.upload();
};

// 暴露方法给父组件
defineExpose({
  triggerUpload,
});
</script>

<style lang="scss" scoped>
.avatar-upload-container {
  display: inline-block;
  position: relative;
}

.avatar-display {
  width: v-bind('props.size + "px"');
  height: v-bind('props.size + "px"');
  border-radius: 50%;
  overflow: hidden;
  cursor: pointer;
  position: relative;
  transition: all 0.3s ease;
  border: 2px solid transparent;

  &:hover {
    border-color: var(--accent-color);
    box-shadow: var(--shadow-accent);
  }
}

.avatar-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  display: block;
}

.avatar-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: v-bind('props.placeholderColor');
  color: white;
  font-size: 12px;
  text-align: center;
}

.upload-icon {
  font-size: 24px;
  margin-bottom: 4px;
}

.upload-text {
  font-size: 18px;
  white-space: nowrap;
}

.avatar-uploading {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(0, 0, 0, 0.7);
  color: white;
}

.upload-progress {
  text-align: center;
}

.progress-circle {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  border: 3px solid rgba(255, 255, 255, 0.3);
  border-top: 3px solid var(--accent-color);
  animation: spin 1s linear infinite;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.progress-text {
  font-size: 10px;
  font-weight: bold;
  color: var(--accent-color);
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.error-message {
  position: absolute;
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  margin-top: 8px;
  padding: 4px 8px;
  background: rgba(255, 0, 0, 0.8);
  color: white;
  border-radius: 4px;
  font-size: 12px;
  white-space: nowrap;
  z-index: 10;
}
</style>
