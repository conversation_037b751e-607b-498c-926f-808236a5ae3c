<template>
  <div v-if="visible" class="dialog-overlay" @click.self="handleCancel">
    <div class="dialog-container" @click.stop>
      <div class="dialog-header">
        <div class="dialog-title">{{ title }}</div>
        <div class="dialog-close" @click="handleCancel">
          <DeleteIcon :size="24" color="var(--primary-color)" />
        </div>
      </div>
      <div class="dialog-content">
        <div class="delete-warning">{{ content }}</div>
        <div v-if="hint" class="delete-hint">{{ hint }}</div>
      </div>
      <div class="dialog-footer">
        <button class="cancel-btn" @click="handleCancel">
          {{ cancelText }}
        </button>
        <button class="delete-confirm-btn" :disabled="isLoading" @click="handleConfirm">
          {{ isLoading ? loadingText : confirmText }}
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { defineProps, defineEmits } from 'vue';
import DeleteIcon from '@/assets/icons/DeleteIcon.vue';

// Props定义
interface IProps {
  visible: boolean;
  title?: string;
  content: string;
  hint?: string;
  confirmText?: string;
  cancelText?: string;
  loadingText?: string;
  isLoading?: boolean;
}

const props = withDefaults(defineProps<IProps>(), {
  title: '确认删除',
  hint: '',
  confirmText: '确认删除',
  cancelText: '取消',
  loadingText: '删除中...',
  isLoading: false,
});

// Emits定义
const emit = defineEmits<{
  confirm: [];
  cancel: [];
}>();

// 处理确认
const handleConfirm = () => {
  if (!props.isLoading) {
    emit('confirm');
  }
};

// 处理取消
const handleCancel = () => {
  if (!props.isLoading) {
    emit('cancel');
  }
};
</script>

<style scoped lang="scss">
// 删除确认对话框样式
.dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(20px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1100;
  animation: fadeIn 0.3s ease-out;
  pointer-events: auto;
}

.dialog-container {
  background: var(--bg-glass-popup);
  border: none;
  border-radius: 16px;
  padding: 30px;
  box-sizing: border-box;
  backdrop-filter: blur(10px);
  border-left: 4px solid var(--accent-color);
  box-shadow: var(--shadow-accent);
  transition: all 0.3s ease;
  width: 90%;
  max-width: 500px;
  color: var(--text-primary);
  animation: slideInScale 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
  display: flex;
  flex-direction: column;
}

.dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid var(--border-accent);
}

.dialog-title {
  font-size: 40px; // 增加4px (原来36px)
  font-weight: 600;
  color: var(--accent-color);
  text-shadow: var(--shadow-accent);
}

.dialog-close {
  cursor: pointer;
  padding: 8px;
  border-radius: 8px;
  transition: all 0.2s ease;
  background: transparent;
  display: flex;
  align-items: center;
  justify-content: center;

  &:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: scale(1.1);
  }

  .close-icon {
    width: 24px;
    height: 24px;
    filter: brightness(0) saturate(100%) invert(100%);
  }
}

.dialog-content {
  margin-bottom: 32px;
  text-align: center;
}

.delete-warning {
  font-size: 36px; // 增加4px (原来32px)
  font-weight: 500;
  color: var(--text-primary);
  margin-bottom: 16px;
  line-height: 1.4;

  strong {
    color: var(--accent-color);
    font-weight: 600;
  }
}

.delete-hint {
  font-size: 32px; // 增加4px (原来28px)
  color: rgba(255, 255, 255, 0.7);
  line-height: 1.4;
  font-style: italic;
}

.dialog-footer {
  display: flex;
  gap: 16px;
  justify-content: center;
}

.cancel-btn,
.delete-confirm-btn {
  flex: 1;
  max-width: 200px;
  padding: 16px 24px;
  border: 2px solid;
  border-radius: 12px;
  font-size: 32px; // 增加4px (原来28px)
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  background: transparent;
  height: 64px; // 增加4px (原来60px)
  display: flex;
  align-items: center;
  justify-content: center;

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }

  &:not(:disabled):hover {
    transform: translateY(-2px);
  }
}

.cancel-btn {
  color: rgba(255, 255, 255, 0.8);
  border-color: rgba(255, 255, 255, 0.3);
  background: rgba(255, 255, 255, 0.1);

  &:hover:not(:disabled) {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.5);
    box-shadow: 0 4px 12px rgba(255, 255, 255, 0.2);
  }
}

.delete-confirm-btn {
  color: #ef4444;
  border-color: #ef4444;

  &:hover:not(:disabled) {
    background: rgba(239, 68, 68, 0.1);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);
  }
}

// 动画定义
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

// 弹窗出现动画
@keyframes slideInScale {
  from {
    opacity: 0;
    transform: scale(0.8) translateY(20px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}
</style>
