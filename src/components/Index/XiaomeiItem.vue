<template>
  <div class="xiaomei-item" @click="handleClick">
    <div class="xiaomei-item-content">
      <div class="xiaomei-item-icon">{{ icon }}</div>
      <div class="xiaomei-item-title">{{ title }}</div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { defineProps, defineEmits } from 'vue';

// 定义props
defineProps<{
  title: string;
  icon: string;
}>();

// 定义emits
const emit = defineEmits<{
  click: [];
}>();

// 处理点击事件
const handleClick = () => {
  emit('click');
};
</script>

<style lang="scss" scoped>
.xiaomei-item {
  background: var(--accent-color-light); // 使用主题变量
  border: 2px solid var(--border-accent);
  border-radius: 16px;
  padding: 24px; // 增加内边距
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  box-shadow: var(--shadow-accent);

  &:hover {
    border-color: var(--accent-color);
    box-shadow: var(--shadow-strong);
    transform: translateY(-2px);
  }

  &:active {
    transform: translateY(0);
  }
}

.xiaomei-item-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  height: 100%;
  min-height: 96px; // 增加最小高度（从80px改为120px）
}

.xiaomei-item-icon {
  font-size: 48px; // 进一步增加图标大小（从40px改为48px）
  margin-bottom: 16px; // 增加间距
  line-height: 1;
}

.xiaomei-item-title {
  color: var(--page-text-primary);
  font-size: 26px; // 进一步增加字体大小（从22px改为26px）
  font-weight: 600;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
  line-height: 1.2;
}
</style>
