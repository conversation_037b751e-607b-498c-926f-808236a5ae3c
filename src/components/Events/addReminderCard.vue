<template>
  <div class="add-reminder-card" @click="handleClick">
    <div class="add-reminder-content">
      <div class="add-icon">
        <PlusIcon class="add" :size="32" />
      </div>
      <div class="add-text">添加提醒</div>
    </div>
  </div>
</template>

<script setup lang="ts">
import PlusIcon from '@/assets/icons/PlusIcon.vue';

const emit = defineEmits<{
  click: [];
}>();

const handleClick = () => {
  emit('click');
};
</script>

<style scoped lang="scss">
.add-reminder-card {
  width: 220px;
  height: 56px; // 固定高度与reminder-item保持一致
  min-height: 56px;
  // 使用bgGlass背景
  background: var(--bg-glass);
  backdrop-filter: blur(10px);
  border-left: 5px solid var(--accent-color);
  box-shadow: 0 6px 18px -6px color-mix(in oklab, var(--accent-color) 45%, transparent), var(--shadow-accent), inset 0 0 0 1px color-mix(in oklab, var(--accent-color) 35%, transparent);
  border-radius: 12px;
  padding: 10px 12px;
  box-sizing: border-box;
  transition: all 0.3s ease;
  cursor: pointer;
  color: var(--page-text-primary);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  position: relative;
  overflow: hidden;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 22px -6px color-mix(in oklab, var(--accent-color) 60%, transparent), var(--shadow-strong), inset 0 0 0 1px color-mix(in oklab, var(--accent-color) 50%, transparent);
    // hover 时使用bg-glass-hover
    background: var(--bg-glass-hover);
    border-style: solid;
  }

  &:active {
    transform: translateY(-1px);
  }
}

.add-reminder-content {
  display: flex;
  flex-direction: row; // 水平排列更紧凑
  align-items: center;
  gap: 8px;
  text-align: center;
}

.add-icon {
  width: 22px;
  height: 22px;
  display: flex;
  justify-content: center;
  align-items: center;
  background: var(--accent-color-light);
  border: 1px solid var(--accent-color-medium);
  border-radius: 50%;
  transition: all 0.3s ease;
}

.add {
  color: var(--text-primary);
  font-size: 14px;
  font-weight: 600;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  /* 提升可读性：去除半透明与反转滤镜 */
}

.add-text {
  color: var(--primary-color);
  font-size: 28px; // 增加4px (原来16px)
  font-weight: 500;
  letter-spacing: 0.5px;
  transition: all 0.3s ease;
}

.add-reminder-card:hover {
  .add-icon {
    background: var(--accent-color-medium);
    transform: scale(1.05);

    .add-image {
      opacity: 1;
    }
  }

  .add-text {
    color: var(--accent-color);
    transform: translateY(-1px);
  }
}
</style>
