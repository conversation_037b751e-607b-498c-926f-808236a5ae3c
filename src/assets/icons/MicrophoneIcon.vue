<template>
  <svg
    t="1755588681677"
    class="icon"
    viewBox="0 0 1024 1024"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg"
    p-id="8814"
    :width="size || '200'"
    :height="size || '200'"
    :stroke="color || 'var(--primary-color)'"
    stroke-width="0.8"
  >
    <path
      d="M486.4 972.8v-128.9728A332.8 332.8 0 0 1 179.2 512a25.6 25.6 0 0 1 51.2 0 281.6 281.6 0 0 0 563.2 0 25.6 25.6 0 1 1 51.2 0 332.8 332.8 0 0 1-307.2 331.8272V972.8h153.6a25.6 25.6 0 1 1 0 51.2h-358.4a25.6 25.6 0 1 1 0-51.2h153.6zM512 51.2a153.6 153.6 0 0 0-153.6 153.6v307.2a153.6 153.6 0 0 0 307.2 0V204.8a153.6 153.6 0 0 0-153.6-153.6z m0-51.2a204.8 204.8 0 0 1 204.8 204.8v307.2a204.8 204.8 0 1 1-409.6 0V204.8a204.8 204.8 0 0 1 204.8-204.8z"
      :fill="color || 'var(--primary-color)'"
      p-id="8815"
    />
  </svg>
</template>

<script setup lang="ts">
interface IProps {
  size?: string | number;
  color?: string;
}

defineProps<IProps>();
</script>

<style scoped>
.icon {
  display: inline-block;
  vertical-align: middle;
  transition: all 0.3s ease;
}

/* 继承父元素的颜色作为默认颜色 */
.icon:not([fill]) {
  fill: currentColor;
}

/* SVG路径加粗 */
.icon path {
  stroke-width: inherit;
  vector-effect: non-scaling-stroke;
}
</style>
