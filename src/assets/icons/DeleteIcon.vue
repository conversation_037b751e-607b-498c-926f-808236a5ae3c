<template>
  <svg
    t="1755586766317"
    class="delete-icon"
    viewBox="0 0 1024 1024"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg"
    p-id="7576"
    :width="size"
    :height="size"
    :style="{ fill: color }"
    :stroke="color"
    stroke-width="0.5"
  >
    <path
      d="M561.17013333 509.06026667L858.02666667 213.73973333c14.03733333-13.968 14.1088-36.60053333 0.1408-50.63786666-13.99893333-14.06826667-36.592-14.10773333-50.62933334-0.1408L510.6048 458.31466667 216.256 163.06986667c-13.9328-13.96693333-36.59733333-14.03733333-50.63466667-0.07146667-14.00426667 13.96586667-14.03733333 36.63146667-0.0704 50.6688l294.27733334 295.1744-296.71466667 295.14026667c-14.0384 13.968-14.1088 36.59733333-0.14293333 50.63786666a35.7216 35.7216 0 0 0 25.3856 10.56c9.13066667 0 18.26666667-3.4688 25.25013333-10.4192l296.78613333-295.2128L807.4304 857.48266667c6.9824 7.02186667 16.15253333 10.53013333 25.35253333 10.53013333a35.72906667 35.72906667 0 0 0 25.28213334-10.45973333c13.99893333-13.96586667 14.03733333-36.592 0.07146666-50.62933334L561.17013333 509.06026667z m0 0"
      :fill="color"
      p-id="7577"
    />
  </svg>
</template>

<script setup lang="ts">
interface IProps {
  size?: string | number;
  color?: string;
}

withDefaults(defineProps<IProps>(), {
  size: '20px',
  color: 'var(--primary-color)',
});
</script>

<style scoped>
.delete-icon {
  transition: fill 0.3s ease;

  path {
    stroke-width: inherit;
    vector-effect: non-scaling-stroke;
  }
}
</style>
