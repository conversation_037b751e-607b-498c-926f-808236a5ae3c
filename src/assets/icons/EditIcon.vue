<template>
  <svg
    t="1755516667458"
    class="icon edit-icon"
    viewBox="0 0 1024 1024"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg"
    p-id="3677"
    :width="width"
    :height="height"
    :style="{ fill: color }"
    :stroke="color"
    stroke-width="0.5"
  >
    <path
      d="M945.4 162.8l-84.9-84.9a48.5 48.5 0 0 0-34.8-14.1 56.5 56.5 0 0 0-39.8 16.8L467.1 399.4a57.8 57.8 0 0 0-15.7 29.1l-30.6 150.4a20 20 0 0 0 19.5 24 19 19 0 0 0 4.1-0.4L594.8 572a56.9 56.9 0 0 0 29.1-15.8l318.8-318.8c21.4-21.3 22.6-54.7 2.7-74.6zM576 502.3l-69 14 14-68.9 213.7-213.7 54.9 55z m253.2-253.2l-54.9-55 50.3-50.3 54.9 55zM892 512h-64a4 4 0 0 0-4 4v308H200V200h308a4 4 0 0 0 4-4v-64a4 4 0 0 0-4-4H168a40 40 0 0 0-40 40v688a40 40 0 0 0 40 40h688a40 40 0 0 0 40-40V516a4 4 0 0 0-4-4z"
      p-id="3678"
    />
  </svg>
</template>

<script setup lang="ts">
interface IProps {
  width?: string | number;
  height?: string | number;
  color?: string;
}

withDefaults(defineProps<IProps>(), {
  width: '20px',
  height: '20px',
  color: 'var(--primary-color)',
});
</script>

<style scoped>
.edit-icon {
  transition: fill 0.3s ease;

  path {
    stroke-width: inherit;
    vector-effect: non-scaling-stroke;
  }
}
</style>
