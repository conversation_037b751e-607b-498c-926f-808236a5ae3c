<template>
  <svg
    t="1755675233061"
    class="icon travel-icon"
    viewBox="0 0 1024 1024"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg"
    p-id="22522"
    :width="width"
    :height="height"
    :style="{ fill: color }"
  >
    <path
      d="M62.966824 724.651022l75.404193 42.804636a118.775778 118.775778 0 0 0 117.64188 113.389765L303.353124 963.903425a116.507983 116.507983 0 0 0 102.050788 58.962677 121.043573 121.043573 0 0 0 105.735955-179.439302L457.563203 750.447194l-4.819065 2.834744 113.389765-95.247402 210.054538 210.338012a125.862638 125.862638 0 0 0 202.684204-141.737205l-141.737206-297.648131 118.775779-100.349942a184.825316 184.825316 0 0 0 64.065216-132.666024A188.227009 188.227009 0 0 0 826.079937 0.090428a185.392265 185.392265 0 0 0-132.949498 64.348692l-101.200365 118.492303-297.648131-141.737205a125.579164 125.579164 0 0 0-141.737206 202.400729L362.032327 453.649486l-93.546556 110.55502-85.042323-48.19065a121.043573 121.043573 0 0 0-175.47066 137.485089 116.507983 116.507983 0 0 0 54.994036 71.152077zM423.2628 935.555984a35.150827 35.150827 0 0 1-26.646595 3.685167 31.46566 31.46566 0 0 1-19.27626-14.740669L336.803104 850.513661l54.427087-46.206329 46.206329 81.073681a35.717776 35.717776 0 0 1-14.17372 50.174971z m477.937856-172.068967a40.536841 40.536841 0 0 1-65.199114 46.206328l-205.518948-206.652845L769.385055 484.831671zM211.790889 184.065321A40.536841 40.536841 0 0 1 259.131116 118.866206l277.237974 131.248653-118.20883 139.46941zM230.783675 741.092538L756.912181 119.433155A97.798672 97.798672 0 0 1 828.064258 85.132752h4.252116a100.349941 100.349941 0 0 1 72.285975 30.331762 100.633416 100.633416 0 0 1 30.331762 76.254616 98.082146 98.082146 0 0 1-34.300404 71.435552L278.69085 789.283188a33.44998 33.44998 0 0 1-45.355906-2.55127A33.44998 33.44998 0 0 1 230.783675 741.092538z m-136.634666-136.351192a35.150827 35.150827 0 0 1 47.340226-13.890246l71.152077 40.253367-46.206328 54.427087L104.921036 652.081573a31.46566 31.46566 0 0 1-15.874567-20.693632 35.717776 35.717776 0 0 1 5.10254-26.646595z"
      :fill="color"
      p-id="22523"
    />
  </svg>
</template>

<script setup lang="ts">
interface IProps {
  width?: string | number;
  height?: string | number;
  color?: string;
}

withDefaults(defineProps<IProps>(), {
  width: '40px',
  height: '40px',
  color: 'var(--primary-color)',
});
</script>

<style scoped>
.travel-icon {
  transition: fill 0.3s ease;
}
</style>
