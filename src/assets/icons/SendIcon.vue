<template>
  <svg
    t="1755671765027"
    class="icon"
    viewBox="0 0 1024 1024"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg"
    p-id="15560"
    :width="size || '200'"
    :height="size || '200'"
  >
    <path
      d="M0 1024l106.496-474.112 588.8-36.864-588.8-39.936L0 0l1024 512z"
      :fill="color || 'var(--primary-color)'"
      p-id="15561"
    />
  </svg>
</template>

<script setup lang="ts">
interface IProps {
  size?: string | number;
  color?: string;
}

defineProps<IProps>();
</script>

<style scoped>
.icon {
  display: inline-block;
  vertical-align: middle;
}
</style>
