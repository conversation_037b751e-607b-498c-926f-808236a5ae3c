<template>
  <svg
    t="1755674408102"
    class="icon info-icon"
    viewBox="0 0 1024 1024"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg"
    p-id="17039"
    :width="width"
    :height="height"
    :style="{ fill: color }"
  >
    <path
      d="M725.333333 85.333333h128a42.666667 42.666667 0 0 1 42.666667 42.666667v768a42.666667 42.666667 0 0 1-42.666667 42.666667H170.666667a42.666667 42.666667 0 0 1-42.666667-42.666667V128a42.666667 42.666667 0 0 1 42.666667-42.666667h128V0h85.333333v85.333333h256V0h85.333333v85.333333z m0 85.333334v85.333333h-85.333333V170.666667H384v85.333333H298.666667V170.666667H213.333333v682.666666h597.333334V170.666667h-85.333334zM298.666667 341.333333h426.666666v85.333334H298.666667V341.333333z m0 170.666667h426.666666v85.333333H298.666667v-85.333333z"
      :fill="color"
      p-id="17040"
    />
  </svg>
</template>

<script setup lang="ts">
interface IProps {
  width?: string | number;
  height?: string | number;
  color?: string;
}

withDefaults(defineProps<IProps>(), {
  width: '40px',
  height: '40px',
  color: 'var(--primary-color)',
});
</script>

<style scoped>
.info-icon {
  transition: fill 0.3s ease;
}
</style>
