<template>
  <svg
    class="relationship-icon"
    viewBox="0 0 1024 1024"
    xmlns="http://www.w3.org/2000/svg"
    :width="width"
    :height="height"
    :style="{ fill: color }"
  >
    <path
      d="M277.333333 213.333333a42.666667 42.666667 0 1 1 85.333334 0 42.666667 42.666667 0 0 1-85.333334 0z m42.666667-128a128 128 0 0 0-42.666667 248.725334V689.92a128 128 0 1 0 85.333334 0v-49.834667c0.192-0.746667 1.216-3.370667 5.738666-8.149333 6.869333-7.253333 18.517333-15.893333 36.053334-25.898667 26.986667-15.36 60.586667-30.101333 98.304-46.634666 12.053333-5.269333 24.533333-10.752 37.333333-16.469334 50.624-22.656 105.642667-49.130667 148.16-82.325333 39.189333-30.592 73.856-71.765333 79.061333-126.336a128 128 0 1 0-86.186666-0.789333c-4.330667 19.2-18.304 38.72-45.397334 59.861333-33.472 26.133333-79.786667 49.002667-130.496 71.68-10.944 4.906667-22.186667 9.834667-33.514666 14.805333-38.528 16.853333-77.866667 34.090667-109.056 51.818667v-197.610667A128 128 0 0 0 320 85.333333z m42.624 555.008l0.042667-0.213333a0.704 0.704 0 0 1-0.042667 0.213333zM682.666667 213.333333a42.666667 42.666667 0 1 1 85.333333 0 42.666667 42.666667 0 0 1-85.333333 0zM320 768a42.666667 42.666667 0 1 0 0 85.333333 42.666667 42.666667 0 0 0 0-85.333333z"
    />
  </svg>
</template>

<script setup lang="ts">
interface IProps {
  width?: string | number;
  height?: string | number;
  color?: string;
}

withDefaults(defineProps<IProps>(), {
  width: '30px',
  height: '30px',
  color: 'currentColor',
});
</script>

<style scoped>
.relationship-icon {
  transition: fill 0.3s ease;
}
</style>
