<template>
  <svg
    t="1755674281174"
    class="icon memory-icon"
    viewBox="0 0 1024 1024"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg"
    p-id="16876"
    :width="width"
    :height="height"
    :style="{ fill: color }"
  >
    <path
      d="M654.66140201 823.35590658H181.37959755V103.28028987h613.3398962v165.31020122h-71.33070099V168.36304627H252.71029856v589.9101039h401.95110345z"
      :fill="color"
      p-id="16877"
    />
    <path
      d="M842.09974041 864.74853963h-135.89279533V363.35098442l67.94639768-56.49183257 67.94639765 56.49183257v501.39755521z m-91.37618998-44.25627436h47.38024665V384.17746644l-23.69012333-19.78515792-23.69012332 19.78515792v436.31479883zM349.29310902 254.27228469h270.74426657v44.25627436h-270.74426657zM350.59476415 350.59476415h270.74426658v44.25627436H350.59476415zM347.9914539 449.52055386h192.6449589v44.25627436h-192.6449589z"
      :fill="color"
      p-id="16878"
    />
  </svg>
</template>

<script setup lang="ts">
interface IProps {
  width?: string | number;
  height?: string | number;
  color?: string;
}

withDefaults(defineProps<IProps>(), {
  width: '40px',
  height: '40px',
  color: 'var(--primary-color)',
});
</script>

<style scoped>
.memory-icon {
  transition: fill 0.3s ease;
}
</style>
