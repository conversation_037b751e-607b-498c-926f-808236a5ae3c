<template>
  <svg
    t="1755675177252"
    class="icon topic-icon"
    viewBox="0 0 1024 1024"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg"
    p-id="19250"
    :width="width"
    :height="height"
    :style="{ fill: color }"
    :stroke="color"
    stroke-width="0.8"
  >
    <path
      d="M512 136c-208 0-376 144-376 320 0 108.8 64 209.6 172.8 268.8 11.2 6.4 25.6 1.6 32-9.6 6.4-11.2 1.6-25.6-9.6-32-92.8-51.2-147.2-136-147.2-227.2 0-150.4 147.2-272 328-272s328 121.6 328 272-147.2 272-328 272c-6.4 0-12.8 3.2-17.6 6.4l-86.4 86.4V752c0-12.8-11.2-24-24-24s-24 11.2-24 24v128c0 9.6 6.4 19.2 14.4 22.4 3.2 1.6 6.4 1.6 9.6 1.6 6.4 0 12.8-3.2 17.6-6.4l121.6-121.6c203.2-4.8 366.4-145.6 366.4-320-1.6-176-169.6-320-377.6-320z"
      :fill="color"
      p-id="19251"
    />
    <path
      d="M312 424h400c12.8 0 24-11.2 24-24s-11.2-24-24-24h-400c-12.8 0-24 11.2-24 24s11.2 24 24 24zM312 568H624c12.8 0 24-11.2 24-24s-11.2-24-24-24H312c-12.8 0-24 11.2-24 24s11.2 24 24 24z"
      :fill="color"
      p-id="19252"
    />
  </svg>
</template>

<script setup lang="ts">
interface IProps {
  width?: string | number;
  height?: string | number;
  color?: string;
}

withDefaults(defineProps<IProps>(), {
  width: '40px',
  height: '40px',
  color: 'var(--primary-color)',
});
</script>

<style scoped>
.topic-icon {
  transition: fill 0.3s ease;

  path {
    stroke-width: inherit;
    vector-effect: non-scaling-stroke;
  }
}
</style>
