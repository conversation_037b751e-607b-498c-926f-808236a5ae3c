<template>
  <svg
    class="home-icon"
    viewBox="0 0 1024 1024"
    xmlns="http://www.w3.org/2000/svg"
    :width="width"
    :height="height"
    :style="{ fill: color }"
  >
    <path
      d="M992.335 448c-6.4 0-12.8-1.6-19.2-6.4L512.335 104l-460.8 337.6c-14.4 11.2-33.6 8-44.8-6.4-11.2-14.4-8-33.6 6.4-44.8l480-352c11.2-8 27.2-8 38.4 0l480 352c14.4 11.2 17.6 30.4 6.4 44.8-6.4 8-16 12.8-25.6 12.8z m-160 512h-192c-17.6 0-32-14.4-32-32V576h-192v352c0 17.6-14.4 32-32 32h-192c-52.8 0-96-43.2-96-96V480c0-17.6 14.4-32 32-32s32 14.4 32 32v384c0 17.6 14.4 32 32 32h160V544c0-17.6 14.4-32 32-32h256c17.6 0 32 14.4 32 32v352h160c17.6 0 32-14.4 32-32V480c0-17.6 14.4-32 32-32s32 14.4 32 32v384c0 52.8-43.2 96-96 96z"
    />
  </svg>
</template>

<script setup lang="ts">
interface IProps {
  width?: string | number;
  height?: string | number;
  color?: string;
}

withDefaults(defineProps<IProps>(), {
  width: '30px',
  height: '30px',
  color: 'currentColor',
});
</script>

<style scoped>
.home-icon {
  transition: fill 0.3s ease;
}
</style>
