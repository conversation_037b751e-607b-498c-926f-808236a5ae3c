<template>
  <svg
    t="1755675370769"
    class="icon food-icon"
    viewBox="0 0 1024 1024"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg"
    p-id="23838"
    :width="width"
    :height="height"
    :style="{ fill: color }"
  >
    <path
      d="M388.657 875.358c-18.782 0-34.007-14.356-34.007-32.066v-46.79c-149.976-61.361-256.221-207.6-259-379l-0.056-6.895v-37.888a37.888 37.888 0 0 1 37.262-37.883l0.627-0.006h290.172l175.457-175.458a40.039 40.039 0 0 1 56.623 56.624L536.902 334.83h81.75l148.484-159.1a40.039 40.039 0 1 1 58.543 54.635L728.185 334.83h163.07a37.889 37.889 0 0 1 37.883 37.262v35.902c0 174.587-106.763 325.5-259.055 388.288v47.01c0 17.71-15.226 32.066-34.007 32.066zM243.903 528.164a35.257 35.257 0 0 0-14.2 47.8A305.559 305.559 0 0 0 378.82 711.508a35.26 35.26 0 0 0 27.522-64.927 235.041 235.041 0 0 1-114.637-104.223 35.238 35.238 0 0 0-47.8-14.194z"
      :fill="color"
      p-id="23839"
    />
  </svg>
</template>

<script setup lang="ts">
interface IProps {
  width?: string | number;
  height?: string | number;
  color?: string;
}

withDefaults(defineProps<IProps>(), {
  width: '40px',
  height: '40px',
  color: 'var(--primary-color)',
});
</script>

<style scoped>
.food-icon {
  transition: fill 0.3s ease;
}
</style>
