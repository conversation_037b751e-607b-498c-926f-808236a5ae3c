<template>
  <svg
    t="1755675415669"
    class="icon expectation-icon"
    viewBox="0 0 1024 1024"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg"
    p-id="24898"
    :width="width"
    :height="height"
    :style="{ fill: color }"
    :stroke="color"
    stroke-width="0.8"
  >
    <path
      d="M862.276267 569.361067l-358.4 307.2a25.6 25.6 0 0 0 33.314133 38.877866l358.4-307.2a25.6 25.6 0 0 0-33.314133-38.877866z"
      :fill="color"
      p-id="24899"
    />
    <path
      d="M145.476267 608.238933l358.4 307.2a25.6 25.6 0 0 0 33.314133-38.877866l-358.4-307.2a25.6 25.6 0 0 0-33.314133 38.877866zM492.663467 697.685333a6292.002133 6292.002133 0 0 1-116.224-98.594133c-62.395733-54.4768-105.745067-95.761067-120.354134-115.6096-25.088-34.116267-27.869867-38.007467-35.413333-50.2784-19.831467-32.3072-27.648-62.122667-27.648-115.165867 0-99.054933 80.315733-179.370667 179.387733-179.370666 37.614933 0 86.4768 30.429867 134.997334 77.021866a25.6 25.6 0 0 0 17.732266 7.133867h4.113067a25.6 25.6 0 0 0 18.107733-7.509333c48.964267-48.9472 91.921067-76.6464 130.5088-76.6464 99.072 0 179.370667 80.315733 179.370667 179.370666 0 64.8192-11.502933 97.877333-42.5984 142.267734-3.584 5.12-17.373867 24.285867-16.776533 23.466666-11.434667 15.9744-59.255467 59.4944-129.4848 118.6304a8043.997867 8043.997867 0 0 1-146.0224 119.739734 7058.773333 7058.773333 0 0 1-29.696-24.456534zM372.411733 87.466667c-127.3344 0-230.570667 103.236267-230.570666 230.570666 0 62.173867 10.222933 101.2736 35.208533 141.960534 8.635733 14.062933 11.537067 18.1248 37.802667 53.828266 17.749333 24.1152 62.2592 66.5088 127.914666 123.835734 5.205333 4.5568 10.530133 9.1648 15.940267 13.858133a7019.229867 7019.229867 0 0 0 147.370667 123.426133 25.6 25.6 0 0 0 32.0512 0.1536l12.970666-10.359466a9024.989867 9024.989867 0 0 0 137.608534-112.5376l12.663466-10.632534c74.922667-63.095467 123.016533-106.871467 138.154667-128.034133-0.768 1.058133 13.2608-18.432 17.066667-23.8592 36.829867-52.599467 51.848533-95.744 51.848533-171.639467 0-127.3344-103.236267-230.570667-230.570667-230.570666-51.387733 0-99.157333 27.784533-151.005866 76.424533-52.224-46.762667-104.516267-76.424533-154.453334-76.424533z m138.752 91.648l18.090667 18.090666v-25.6h-4.113067v25.6l17.732267-18.449066c-5.341867-5.12-10.666667-10.0864-16.008533-14.865067-5.188267 4.864-10.427733 9.949867-15.701334 15.223467z"
      :fill="color"
      p-id="24900"
    />
  </svg>
</template>

<script setup lang="ts">
interface IProps {
  width?: string | number;
  height?: string | number;
  color?: string;
}

withDefaults(defineProps<IProps>(), {
  width: '40px',
  height: '40px',
  color: 'var(--primary-color)',
});
</script>

<style scoped>
.expectation-icon {
  transition: fill 0.3s ease;

  path {
    stroke-width: inherit;
    vector-effect: non-scaling-stroke;
  }
}
</style>
