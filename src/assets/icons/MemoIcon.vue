<template>
  <svg
    class="memo-icon"
    viewBox="0 0 1024 1024"
    xmlns="http://www.w3.org/2000/svg"
    :width="width"
    :height="height"
    :style="{ fill: color }"
  >
    <path
      d="M177.210028 920.706449c-6.78452 0-12.283768-5.194302-12.283768-11.603269l0-38.192857c0-6.407943 5.499248-11.603269 12.283768-11.603269l720.797443 0L898.007471 93.63661c0-16.019859-12.986779-29.007661-29.006637-29.007661l-132.159996 0 0 340.198146c0 4.514827-4.930289 7.299243-8.79634 4.968152l-88.609107-53.422723c-1.910513-1.152243-4.313236-1.106194-6.178723 0.11768l-80.317257 52.712549c-3.857864 2.53166-8.984628-0.23536-8.984628-4.850471L543.954783 64.62895 156.071591 64.62895c-16.019859 0-29.006637 12.986779-29.006637 29.007661l0 836.31848c0 16.019859 12.986779 29.006637 29.006637 29.006637l712.929242 0c16.019859 0 29.006637-12.986779 29.006637-29.006637l0-9.247618L177.210028 920.707472z"
    />
  </svg>
</template>

<script setup lang="ts">
interface IProps {
  width?: string | number;
  height?: string | number;
  color?: string;
}

withDefaults(defineProps<IProps>(), {
  width: '40px',
  height: '40px',
  color: 'currentColor',
});
</script>

<style scoped>
.memo-icon {
  transition: fill 0.3s ease;
}
</style>
