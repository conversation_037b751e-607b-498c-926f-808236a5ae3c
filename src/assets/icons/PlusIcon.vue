<template>
  <svg
    t="1755585988625"
    class="plus-icon"
    viewBox="0 0 1024 1024"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg"
    p-id="6588"
    :width="size"
    :height="size"
    :style="{ fill: color }"
    :stroke="color"
    stroke-width="0.6"
  >
    <path
      d="M469.333333 469.333333V170.666667h85.333334v298.666666h298.666666v85.333334h-298.666666v298.666666h-85.333334v-298.666666H170.666667v-85.333334h298.666666z"
      :fill="color"
      p-id="6589"
    />
  </svg>
</template>

<script setup lang="ts">
interface IProps {
  size?: string | number;
  color?: string;
}

withDefaults(defineProps<IProps>(), {
  size: '20px',
  color: 'var(--primary-color)',
});
</script>

<style scoped>
.plus-icon {
  transition: fill 0.3s ease;
}
path {
  stroke-width: inherit;
  vector-effect: non-scaling-stroke;
}
</style>
